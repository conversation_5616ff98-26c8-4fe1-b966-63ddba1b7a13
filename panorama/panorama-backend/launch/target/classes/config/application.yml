# 应用服务基础配置
server:
  address: 0.0.0.0
  port: ${SERVER_PORT}
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 日志配置
logging:
  config: classpath:config/log4j2.xml
  level:
    org.nebula.contrib: DEBUG

spring:
  config:
    import: optional:./config.properties
  # 数据库配置
  datasource:
    url: ${PG_DB_URL}
    username: ${PG_DB_USER}
    password: ${PG_DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 4
      connection-timeout: 30000
      idle-timeout: 600000
      pool-name: PostgreSQLHikariCP
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    open-in-view: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_BROKERS}
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: 1
      properties:
        spring.json.add.type.headers: false
  # 定时任务配置
  quartz:
    job-store-type: memory
    properties:
      org:
        quartz:
          scheduler:
            instanceName: PresetModelScheduler
          threadPool:
            threadCount: 20  # 根据CPU核心数调整(建议4-8倍核心数)
            threadPriority: 5
            idleThreadTimeout: 300000  # 30秒空闲后回收(ms)
          jobStore:
            class: org.quartz.simpl.RAMJobStore
            batchTriggerAcquisitionMaxCount: 10  # 每次最多获取10个触发
            misfireThreshold: 60000
    auto-startup: true
    startup-delay: 5
    wait-for-jobs-to-complete-on-shutdown: true
    overwrite-existing-jobs: false
  # Http请求编码
  # 限定文件上传大小
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# mybatis-plus配置
mybatis-plus:
  global-config:
    banner: false
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl

# Nebula图数据库配置
nebula:
  ngbatis:
    use-session-pool: true
  hosts: ${NEBULA_ADDRESS}
  username: ${NEBULA_USER}
  password: ${NEBULA_PASSWORD}
  space: ${NEBULA_SPACE_NAME}
  pool-config:
    min-conns-size: 0
    max-conns-size: 10
    timeout: 0
    idle-time: 0
    interval-idle: -1
    wait-time: 120
    min-cluster-health-rate: 1.0
    enable-ssl: false
    max_sessions_per_ip_per_user: 100
# 防止与mybatis冲突，单独配置mapper扫描路径
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

# 外部调用URL
url:
  login_status_check: ${LOGIN_STATUS_CHECK_URL}

# 线程异步配置
async:
  executor:
    core-pool-size: 2
    max-pool-size: 2
    queue-capacity: 500
    thread-name-prefix: preset-model-trigger-

#各项功能启用开关
enabled:
  atlas: false
  nebula: true
  redis: true
  task_scheduled: false
  login_check: true
