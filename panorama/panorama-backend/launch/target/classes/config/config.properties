# 应用访问端口
SERVER_PORT=10009

# PostgreSQL数据库连接信息
PG_DB_URL=************************************************************************
PG_DB_USER=agg_judgment
PG_DB_PASSWORD=agg_judgment

# Kakfa配置
KAFKA_BROKERS=***************:9092

# Nebula配置
NEBULA_ADDRESS=***************:9669
NEBULA_USER=root
NEBULA_PASSWORD=nebula
NEBULA_SPACE_NAME=apt_target_judgment_graph

# 权限认证URL
LOGIN_STATUS_CHECK_URL=http://***************:10005/auth/validateUser