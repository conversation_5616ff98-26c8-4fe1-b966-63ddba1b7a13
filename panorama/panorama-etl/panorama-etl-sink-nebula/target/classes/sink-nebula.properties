kafka.bootstrap.server.alert = ***************:9092
kafka.bootstrap.server.filter = ***************:9092

kafka.group.id.alert = alert_log_etl
kafka.group.id.nebula = nebula_log_etl

kafka.topic.alert = alert_log
kafka.topic.filtered = filtered_log

redis.host.addr = ***************
redis.host.port = 6379

flink.alert.filter.parallelism = 8
flink.alert.aggr.parallelism = 8

flink.window.minutes = 1
flink.window.seconds = 10
flink.window.count.seconds = 10

flink.process.parallelism = 8
flink.nebula.sink.parallelism= 8
flink.nebula.sink.interval = 3000

nebula.graph.addr = ***************:9669
nebula.meta.addr = ***************:9559
nebula.space.name = apt_target_judgment_graph
nebula.vertex.batch.sink.num = 200
nebula.edge.batch.sink.num = 200