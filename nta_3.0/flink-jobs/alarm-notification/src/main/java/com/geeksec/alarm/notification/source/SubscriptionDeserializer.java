package com.geeksec.alarm.notification.source;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarm.notification.model.NotificationSubscription;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 订阅配置反序列化器
 * 将 Kafka 消息反序列化为 NotificationSubscriptionDto 对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionDeserializer implements DeserializationSchema<NotificationSubscription> {
    
    private static final long serialVersionUID = 1L;
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        log.info("订阅配置反序列化器已初始化");
    }
    
    @Override
    public NotificationSubscription deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            String json = new String(message, StandardCharsets.UTF_8);
            log.debug("反序列化订阅配置消息: {}", json);
            
            NotificationSubscription subscription = objectMapper.readValue(message, NotificationSubscription.class);
            
            // 基本验证
            if (subscription.getSubscriptionId() == null || subscription.getSubscriptionId().trim().isEmpty()) {
                log.warn("订阅ID为空，跳过处理");
                return null;
            }
            
            return subscription;
            
        } catch (Exception e) {
            log.error("反序列化订阅配置消息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(NotificationSubscription nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<NotificationSubscription> getProducedType() {
        return TypeInformation.of(NotificationSubscription.class);
    }
}
