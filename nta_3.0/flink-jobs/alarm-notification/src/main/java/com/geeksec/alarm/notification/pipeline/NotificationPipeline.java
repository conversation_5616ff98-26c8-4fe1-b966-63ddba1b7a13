package com.geeksec.alarm.notification.pipeline;

import com.geeksec.alarm.notification.config.AlarmNotificationConfig;
import com.geeksec.alarm.notification.model.NotificationResult;
import com.geeksec.alarm.notification.model.NotificationSubscription;
import com.geeksec.alarm.notification.function.NotificationFunction;
import com.geeksec.alarm.notification.model.Alarm;
import com.geeksec.alarm.notification.source.AlarmDeserializer;
import com.geeksec.alarm.notification.source.SubscriptionConfigSource;
import com.geeksec.alarm.notification.source.SubscriptionDeserializer;
import com.geeksec.alarm.notification.client.AlarmServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 通知处理流水线
 * 构建完整的告警通知处理流程
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class NotificationPipeline {
    
    private final AlarmNotificationConfig config;
    
    public NotificationPipeline(AlarmNotificationConfig config) {
        this.config = config;
    }
    
    /**
     * 构建通知处理流水线
     * 
     * @param env Flink执行环境
     * @return 通知结果数据流
     */
    public DataStream<NotificationResult> buildPipeline(StreamExecutionEnvironment env) {
        log.info("开始构建通知处理流水线");
        
        // 1. 创建告警数据源（从 Kafka 消费处理后的告警）
        DataStream<Alarm> alarmStream = createAlarmSource(env);
        
        // 2. 创建订阅配置数据源
        DataStream<NotificationSubscription> subscriptionStream = createSubscriptionSource(env);
        
        // 3. 创建订阅配置广播流
        BroadcastStream<NotificationSubscription> subscriptionBroadcastStream = 
                subscriptionStream.broadcast(NotificationFunction.SUBSCRIPTION_STATE_DESCRIPTOR);
        
        // 4. 连接告警流和订阅配置广播流，处理通知
        SingleOutputStreamOperator<NotificationResult> notificationResultStream = alarmStream
                .connect(subscriptionBroadcastStream)
                .process(new NotificationFunction())
                .name("notification-processor")
                .setParallelism(config.getNotificationParallelism());
        
        log.info("通知处理流水线构建完成");
        return notificationResultStream;
    }
    
    /**
     * 创建告警数据源
     */
    private DataStream<Alarm> createAlarmSource(StreamExecutionEnvironment env) {
        log.info("创建告警数据源，主题: {}", config.getInputTopic());
        
        KafkaSource<Alarm> kafkaSource = KafkaSource.<Alarm>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setTopics(config.getInputTopic())
                .setGroupId(config.getConsumerGroupId())
                .setStartingOffsets(getOffsetsInitializer())
                .setValueOnlyDeserializer(new AlarmDeserializer())
                .build();
        
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "alarm-source")
                .setParallelism(config.getKafkaSourceParallelism());
    }
    
    /**
     * 创建订阅配置数据源
     */
    private DataStream<NotificationSubscription> createSubscriptionSource(StreamExecutionEnvironment env) {
        log.info("创建订阅配置数据源");
        
        // 方式1: 初始化时从告警服务获取订阅配置
        AlarmServiceClient alarmServiceClient = new AlarmServiceClient(config.getAlarmServiceBaseUrl());
        DataStream<NotificationSubscription> initialStream = env
                .addSource(SubscriptionConfigSource.createDefault(alarmServiceClient))
                .name("subscription-config-source")
                .setParallelism(1);
        
        // 方式2: 从 Kafka 消费订阅配置变更事件
        DataStream<NotificationSubscription> changeEventStream = createSubscriptionChangeEventSource(env);
        
        // 合并两个数据源
        return initialStream.union(changeEventStream);
    }
    
    /**
     * 创建订阅配置变更事件源
     */
    private DataStream<NotificationSubscription> createSubscriptionChangeEventSource(StreamExecutionEnvironment env) {
        log.info("创建订阅配置变更事件源，主题: {}", config.getSubscriptionChangesTopic());
        
        KafkaSource<NotificationSubscription> kafkaSource = KafkaSource.<NotificationSubscription>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setTopics(config.getSubscriptionChangesTopic())
                .setGroupId(config.getConsumerGroupId() + "-subscription")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SubscriptionDeserializer())
                .build();
        
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "subscription-change-source")
                .setParallelism(1);
    }
    
    /**
     * 获取偏移量初始化器
     */
    private OffsetsInitializer getOffsetsInitializer() {
        switch (config.getStartingOffsets().toLowerCase()) {
            case "earliest":
                return OffsetsInitializer.earliest();
            case "latest":
                return OffsetsInitializer.latest();
            default:
                return OffsetsInitializer.latest();
        }
    }
    
    /**
     * 创建默认通知流水线
     */
    public static NotificationPipeline createDefault(AlarmNotificationConfig config) {
        return new NotificationPipeline(config);
    }
}
