package com.geeksec.certificate.analyzer.job;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;


import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.sink.SinkCoordinator;
import com.geeksec.certificate.analyzer.pipeline.CertificateAnalysisPipeline;
import com.geeksec.certificate.analyzer.source.CertificateSourceFactory;
import com.geeksec.certificate.analyzer.repository.CertificateRepository;
import com.geeksec.certificate.analyzer.repository.doris.DorisCertificateRepository;
import com.geeksec.certificate.analyzer.repository.DorisConnectionProvider;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书分析作业
 *
 * 业务流程：
 * 1. 接收证书文件数据流
 * 2. 进行证书解析和标签分析
 * 3. 生成多种输出：
 * - 证书数据写入Doris表
 * - 证书关系写入Nebula图数据库
 * - 证书文件存储到MinIO
 * - 证书信息写入PostgreSQL（可选）
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateAnalysisJob {



    public static void main(String[] args) throws Exception {
        log.info("启动证书分析作业");

        // 使用证书分析器配置管理器获取配置
        final ParameterTool config = CertificateAnalyzerConfig.getConfig();

        // 合并命令行参数（命令行参数优先级更高）
        final ParameterTool parameterTool = ParameterTool.fromArgs(args).mergeWith(config);

        // 打印配置信息（调试模式下）
        CertificateAnalyzerConfig.printConfig();

        // 创建 Doris 连接提供者和证书仓库
        DorisConnectionProvider dorisConnectionProvider = new DorisConnectionProvider();
        CertificateRepository certificateRepository = new DorisCertificateRepository(dorisConnectionProvider);

        // 创建执行环境
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 设置全局参数
        env.getConfig().setGlobalJobParameters(parameterTool);


        // 创建证书数据源
        DataStream<X509Certificate> certificateStream = createCertificateSource(env);

        // 构建证书分析流水线
        CertificateAnalysisPipeline.PipelineResult pipelineResult = CertificateAnalysisPipeline.build(certificateStream,
                parameterTool);

        // 配置输出协调器
        SinkCoordinator.configureAllOutputs(pipelineResult, parameterTool);

        // 执行作业
        env.execute("证书分析作业");

        log.info("证书分析作业执行完成");
    }


    /**
     * 创建证书数据源
     *
     * @param env Flink执行环境
     * @return 证书数据流
     */
    private static DataStream<X509Certificate> createCertificateSource(StreamExecutionEnvironment env) {
        log.info("创建证书数据源");

        // 获取全局配置
        ParameterTool config = (ParameterTool) env.getConfig().getGlobalJobParameters();

        // 使用数据源工厂创建证书数据源
        return CertificateSourceFactory.createCertificateSource(env, config);
    }


}
