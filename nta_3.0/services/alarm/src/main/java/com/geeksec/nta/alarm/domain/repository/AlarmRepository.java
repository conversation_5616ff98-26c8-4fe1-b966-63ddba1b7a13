package com.geeksec.nta.alarm.domain.repository;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.common.entity.PageResultVo;

import java.util.List;
import java.util.Optional;

/**
 * 告警聚合根仓储接口
 * 定义告警聚合的持久化操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmRepository {
    
    /**
     * 保存告警
     * 
     * @param alarm 告警聚合根
     * @return 保存后的告警
     */
    Alarm save(Alarm alarm);
    
    /**
     * 根据ID查找告警
     * 
     * @param alarmId 告警ID
     * @return 告警聚合根
     */
    Optional<Alarm> findById(AlarmId alarmId);
    
    /**
     * 根据索引和ID查找告警
     * 
     * @param index ES索引
     * @param alarmId 告警ID
     * @return 告警聚合根
     */
    Optional<Alarm> findByIndexAndId(String index, String alarmId);
    
    /**
     * 删除告警
     * 
     * @param alarmId 告警ID
     * @return 是否删除成功
     */
    boolean deleteById(AlarmId alarmId);
    
    /**
     * 批量删除告警
     * 
     * @param alarmIds 告警ID列表
     * @return 删除的数量
     */
    int batchDelete(List<AlarmId> alarmIds);
    
    /**
     * 分页查询告警
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<Alarm> findByPage(AlarmQuery query);
    
    /**
     * 统计告警数量
     * 
     * @param query 查询条件
     * @return 告警数量
     */
    long count(AlarmQuery query);
    
    /**
     * 检查告警是否存在
     * 
     * @param alarmId 告警ID
     * @return 是否存在
     */
    boolean existsById(AlarmId alarmId);
    
    /**
     * 删除所有告警
     * 
     * @return 删除的数量
     */
    long deleteAll();
    
    /**
     * 告警查询条件
     */
    interface AlarmQuery {
        // 查询条件接口，具体实现在application层
    }
}
