package com.geeksec.nta.alarm.application.service.integration;

import com.geeksec.nta.alarm.application.query.AlarmExportQuery;
import com.geeksec.nta.alarm.interfaces.dto.response.ExportTaskResponse;

import java.util.List;

/**
 * 告警导出应用服务
 * 负责处理告警数据导出相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmExportService {
    
    /**
     * 导出告警数据为CSV格式
     * 
     * @param query 导出查询条件
     * @return 导出任务ID
     */
    String exportToCsv(AlarmExportQuery query);
    
    /**
     * 导出告警数据为Excel格式
     * 
     * @param query 导出查询条件
     * @return 导出任务ID
     */
    String exportToExcel(AlarmExportQuery query);
    
    /**
     * 导出告警数据为JSON格式
     * 
     * @param query 导出查询条件
     * @return 导出任务ID
     */
    String exportToJson(AlarmExportQuery query);
    
    /**
     * 获取导出任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    ExportTaskResponse getExportTaskStatus(String taskId);
    
    /**
     * 获取用户的导出任务列表
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<ExportTaskResponse> getUserExportTasks(String userId);
    
    /**
     * 取消导出任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelExportTask(String taskId, String userId);
    
    /**
     * 删除导出任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteExportTask(String taskId, String userId);
    
    /**
     * 准备告警会话PCAP下载
     * 
     * @param userId 用户ID
     * @param sessionIds 会话ID列表
     * @param alarmType 告警类型
     * @param alarmTime 告警时间
     * @return 下载准备结果
     */
    PcapDownloadResponse prepareAlarmSessionPcap(
        String userId, 
        List<String> sessionIds, 
        String alarmType, 
        Long alarmTime
    );
    
    /**
     * PCAP下载响应
     */
    record PcapDownloadResponse(
        String downloadId,
        String status,
        int totalFiles,
        long totalSize,
        String downloadUrl,
        String message
    ) {}
}
