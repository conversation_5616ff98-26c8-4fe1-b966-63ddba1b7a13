package com.geeksec.nta.alarm.interfaces.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AlarmTargetAggVo {

    /**
     * 告警总数
     */
    @JsonProperty("alarm_cnt")
    private long alarmCnt;

    @JsonProperty("high_level")
    private long highLevel;

    @JsonProperty("middle_level")
    private long middleLevel;

    @JsonProperty("low_level")
    private long lowLevel;

    /** 0:未处理 1:确认 2:误报 */
    @JsonProperty("alarm_status0")
    private long alarmStatus0;

    @JsonProperty("alarm_status1")
    private long alarmStatus1;

    @JsonProperty("alarm_status2")
    private long alarmStatus2;

    //防御/模型/规则
    //    @JsonProperty("alarm_type1")
    //
    //    @JsonProperty("alarm_type2")
    //
    //    @JsonProperty("alarm_type3")
}
