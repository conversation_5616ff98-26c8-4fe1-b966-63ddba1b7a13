package com.geeksec.nta.alarm.domain.event;

import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionRule;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * 抑制规则创建事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@RequiredArgsConstructor
public class SuppressionRuleCreatedEvent {
    
    /**
     * 抑制规则ID
     */
    private final SuppressionId suppressionId;
    
    /**
     * 抑制规则
     */
    private final SuppressionRule rule;
    
    /**
     * 操作人
     */
    private final String operator;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt = LocalDateTime.now();
}
