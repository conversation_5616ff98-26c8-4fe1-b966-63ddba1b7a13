package com.geeksec.nta.alarm.interfaces.converter;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.interfaces.dto.suppression.AlarmSuppressionResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警抑制转换器
 * 负责实体与响应对象之间的转换
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
public class AlarmSuppressionConverter {

    /**
     * 转换为响应对象
     */
    public AlarmSuppressionResponse toResponse(AlarmSuppression suppression) {
        if (suppression == null) {
            return null;
        }

        return AlarmSuppressionResponse.builder()
                .suppressionId(suppression.getId().longValue())
                .ruleName(buildRuleName(suppression))
                .description(suppression.getDescription())
                .suppressionType("IP_BASED") // 基于实际字段推断
                .enabled(suppression.getEnabled())
                .priority(1) // 默认优先级
                .conditions(buildConditions(suppression))
                .action("SUPPRESS") // 默认动作
                .durationMinutes(calculateDuration(suppression))
                .effectiveTimeRanges(null) // 当前实体没有时间范围字段
                .attributes(null) // 当前实体没有扩展属性字段
                .createdBy(suppression.getOperator())
                .createTime(suppression.getCreateTime())
                .updatedBy(suppression.getOperator())
                .updateTime(suppression.getUpdateTime())
                .lastTriggeredTime(null) // 当前实体没有此字段
                .triggerCount(0) // 当前实体没有此字段
                .suppressedAlarmCount(0) // 当前实体没有此字段
                .build();
    }

    /**
     * 批量转换为响应对象
     */
    public List<AlarmSuppressionResponse> toResponseList(List<AlarmSuppression> suppressions) {
        if (suppressions == null) {
            return null;
        }
        return suppressions.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * 构建规则名称
     */
    private String buildRuleName(AlarmSuppression suppression) {
        StringBuilder name = new StringBuilder();
        if (suppression.getVictim() != null && !suppression.getVictim().isEmpty()) {
            name.append("victim:").append(suppression.getVictim());
        }
        if (suppression.getAttacker() != null && !suppression.getAttacker().isEmpty()) {
            if (name.length() > 0) name.append(",");
            name.append("attacker:").append(suppression.getAttacker());
        }
        if (suppression.getLabel() != null && !suppression.getLabel().isEmpty()) {
            if (name.length() > 0) name.append(",");
            name.append("label:").append(suppression.getLabel());
        }
        return name.length() > 0 ? name.toString() : "抑制规则-" + suppression.getId();
    }

    /**
     * 构建抑制条件
     */
    private List<AlarmSuppressionResponse.SuppressionCondition> buildConditions(AlarmSuppression suppression) {
        List<AlarmSuppressionResponse.SuppressionCondition> conditions = new java.util.ArrayList<>();
        
        if (suppression.getVictim() != null && !suppression.getVictim().isEmpty()) {
            conditions.add(AlarmSuppressionResponse.SuppressionCondition.builder()
                    .field("victim")
                    .operator("EQUALS")
                    .value(suppression.getVictim())
                    .build());
        }
        
        if (suppression.getAttacker() != null && !suppression.getAttacker().isEmpty()) {
            conditions.add(AlarmSuppressionResponse.SuppressionCondition.builder()
                    .field("attacker")
                    .operator("EQUALS")
                    .value(suppression.getAttacker())
                    .build());
        }
        
        if (suppression.getLabel() != null && !suppression.getLabel().isEmpty()) {
            conditions.add(AlarmSuppressionResponse.SuppressionCondition.builder()
                    .field("label")
                    .operator("EQUALS")
                    .value(suppression.getLabel())
                    .build());
        }
        
        return conditions;
    }

    /**
     * 计算持续时间（分钟）
     */
    private Integer calculateDuration(AlarmSuppression suppression) {
        if (suppression.getExpireTime() != null && suppression.getCreateTime() != null) {
            return (int) java.time.Duration.between(suppression.getCreateTime(), suppression.getExpireTime()).toMinutes();
        }
        return null;
    }
}