package com.geeksec.nta.alarm.application.service.query;

import com.geeksec.common.entity.PageResultVoVo;
import com.geeksec.nta.alarm.application.query.SubscriptionListQuery;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.domain.valueobject.UserId;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSubscriptionResponse;

import java.util.List;
import java.util.Optional;

/**
 * 告警订阅查询应用服务
 * 负责处理所有订阅查询相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSubscriptionQueryService {
    
    /**
     * 分页查询用户订阅
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVoVo<AlarmSubscriptionResponse> querySubscriptions(SubscriptionListQuery query);
    
    /**
     * 获取订阅详情
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @return 订阅详情
     */
    Optional<AlarmSubscriptionResponse> getSubscription(SubscriptionId subscriptionId, UserId userId);
    
    /**
     * 获取用户的所有订阅
     * 
     * @param userId 用户ID
     * @return 订阅列表
     */
    List<AlarmSubscriptionResponse> getUserSubscriptions(UserId userId);
    
    /**
     * 获取用户的活跃订阅
     * 
     * @param userId 用户ID
     * @return 活跃订阅列表
     */
    List<AlarmSubscriptionResponse> getUserActiveSubscriptions(UserId userId);
    
    /**
     * 检查订阅是否存在
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsSubscription(SubscriptionId subscriptionId, UserId userId);
    
    /**
     * 检查订阅名称是否已被使用
     * 
     * @param subscriptionName 订阅名称
     * @param userId 用户ID
     * @param excludeId 排除的订阅ID（用于更新时检查）
     * @return 是否已被使用
     */
    boolean isSubscriptionNameUsed(String subscriptionName, UserId userId, SubscriptionId excludeId);
    
    /**
     * 统计用户订阅数量
     * 
     * @param userId 用户ID
     * @return 订阅数量
     */
    long countUserSubscriptions(UserId userId);
    
    /**
     * 统计用户活跃订阅数量
     * 
     * @param userId 用户ID
     * @return 活跃订阅数量
     */
    long countUserActiveSubscriptions(UserId userId);
    
    /**
     * 获取系统中所有活跃的订阅
     * 用于告警匹配处理
     * 
     * @return 活跃订阅列表
     */
    List<AlarmSubscriptionResponse> getAllActiveSubscriptions();
}
