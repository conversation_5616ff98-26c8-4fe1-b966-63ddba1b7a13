package com.geeksec.nta.alarm.application.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 告警详情查询对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmDetailQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警ID
     */
    @NotBlank(message = "告警ID不能为空")
    private String alarmId;
    
    /**
     * 是否包含详细信息
     */
    @Builder.Default
    private Boolean includeDetails = true;
    
    /**
     * 是否包含关联信息
     */
    @Builder.Default
    private Boolean includeRelations = false;
    
    /**
     * 是否包含处理历史
     */
    @Builder.Default
    private Boolean includeHistory = false;
    
    /**
     * 验证查询参数
     */
    public void validate() {
        if (alarmId == null || alarmId.trim().isEmpty()) {
            throw new IllegalArgumentException("告警ID不能为空");
        }
    }
}