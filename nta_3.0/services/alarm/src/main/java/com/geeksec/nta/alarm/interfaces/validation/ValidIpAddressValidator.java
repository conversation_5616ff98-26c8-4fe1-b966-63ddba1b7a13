package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * IP地址验证器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class ValidIpAddressValidator implements ConstraintValidator<ValidIpAddress, String> {
    
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$"
    );
    
    @Override
    public void initialize(ValidIpAddress constraintAnnotation) {
        // 初始化方法，可以为空
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            return true; // null或空值由其他注解处理
        }
        
        String trimmedValue = value.trim();
        return IPV4_PATTERN.matcher(trimmedValue).matches() || 
               IPV6_PATTERN.matcher(trimmedValue).matches();
    }
}