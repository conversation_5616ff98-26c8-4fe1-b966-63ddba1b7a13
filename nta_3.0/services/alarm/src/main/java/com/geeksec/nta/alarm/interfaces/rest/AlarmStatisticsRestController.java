package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.nta.alarm.application.service.AlarmStatisticsService;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmStatisticsRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmStatisticsResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmTrendResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AttackChainResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警统计REST控制器
 * 负责告警统计分析相关的API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/alarms")
@RequiredArgsConstructor
@Tag(name = "告警统计", description = "告警统计分析相关接口")
public class AlarmStatisticsRestController {
    
    private final AlarmStatisticsService alarmStatisticsService;
    
    /**
     * 获取告警统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取告警统计信息", description = "获取各级别告警数量、攻击者数量、受害者数量等统计信息")
    public ApiResponse<AlarmStatisticsResponse> getAlarmStatistics(
            @Valid @ModelAttribute AlarmStatisticsRequest request) {
        
        log.info("获取告警统计信息: {}", request);
        
        var query = request.toQuery();
        var statistics = alarmStatisticsService.getAlarmStatistics(query);
        
        return ApiResponse.success(statistics, "统计查询成功");
    }
    
    /**
     * 获取告警趋势分析
     */
    @GetMapping("/trends")
    @Operation(summary = "获取告警趋势分析", description = "获取指定时间范围内的告警趋势数据")
    public ApiResponse<AlarmTrendResponse> getAlarmTrend(
            @Parameter(description = "开始时间") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "时间粒度", example = "hour") 
            @RequestParam(defaultValue = "hour") String granularity) {
        
        log.info("获取告警趋势分析: startTime={}, endTime={}, granularity={}", 
                startTime, endTime, granularity);
        
        var trend = alarmStatisticsService.getAlarmTrend(startTime, endTime, granularity);
        
        return ApiResponse.success(trend, "趋势分析查询成功");
    }
    
    /**
     * 获取攻击链统计
     */
    @GetMapping("/attack-chains")
    @Operation(summary = "获取攻击链统计", description = "获取告警攻击链路的聚合统计信息")
    public ApiResponse<List<AttackChainResponse>> getAttackChainStatistics(
            @Valid @ModelAttribute AlarmStatisticsRequest request) {
        
        log.info("获取攻击链统计: {}", request);
        
        var query = request.toQuery();
        var attackChains = alarmStatisticsService.getAttackChainStatistics(query);
        
        return ApiResponse.success(attackChains, "攻击链统计查询成功");
    }
    
    /**
     * 获取TOP攻击者统计
     */
    @GetMapping("/top-attackers")
    @Operation(summary = "获取TOP攻击者统计", description = "获取攻击频次最高的攻击者列表")
    public ApiResponse<List<AlarmStatisticsService.AttackerStatistics>> getTopAttackers(
            @Valid @ModelAttribute AlarmStatisticsRequest request,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("获取TOP攻击者统计: request={}, limit={}", request, limit);
        
        var query = request.toQuery();
        var topAttackers = alarmStatisticsService.getTopAttackers(query, limit);
        
        return ApiResponse.success(topAttackers, "TOP攻击者统计查询成功");
    }
    
    /**
     * 获取TOP受害者统计
     */
    @GetMapping("/top-victims")
    @Operation(summary = "获取TOP受害者统计", description = "获取被攻击频次最高的受害者列表")
    public ApiResponse<List<AlarmStatisticsService.VictimStatistics>> getTopVictims(
            @Valid @ModelAttribute AlarmStatisticsRequest request,
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("获取TOP受害者统计: request={}, limit={}", request, limit);
        
        var query = request.toQuery();
        var topVictims = alarmStatisticsService.getTopVictims(query, limit);
        
        return ApiResponse.success(topVictims, "TOP受害者统计查询成功");
    }
    
    /**
     * 获取告警类型分布统计
     */
    @GetMapping("/type-distribution")
    @Operation(summary = "获取告警类型分布统计", description = "获取各种告警类型的数量分布")
    public ApiResponse<List<AlarmStatisticsService.AlarmTypeStatistics>> getAlarmTypeDistribution(
            @Valid @ModelAttribute AlarmStatisticsRequest request) {
        
        log.info("获取告警类型分布统计: {}", request);
        
        var query = request.toQuery();
        var distribution = alarmStatisticsService.getAlarmTypeDistribution(query);
        
        return ApiResponse.success(distribution, "告警类型分布统计查询成功");
    }
    
    /**
     * 获取告警严重程度分布统计
     */
    @GetMapping("/severity-distribution")
    @Operation(summary = "获取告警严重程度分布统计", description = "获取各种严重程度的告警数量分布")
    public ApiResponse<List<AlarmStatisticsService.SeverityStatistics>> getSeverityDistribution(
            @Valid @ModelAttribute AlarmStatisticsRequest request) {
        
        log.info("获取告警严重程度分布统计: {}", request);
        
        var query = request.toQuery();
        var distribution = alarmStatisticsService.getSeverityDistribution(query);
        
        return ApiResponse.success(distribution, "告警严重程度分布统计查询成功");
    }
}
