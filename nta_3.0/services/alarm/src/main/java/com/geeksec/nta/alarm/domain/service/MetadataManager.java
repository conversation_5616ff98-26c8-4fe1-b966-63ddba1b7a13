package com.geeksec.nta.alarm.domain.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 元数据管理器
 * 统一管理各种类型的元数据提供者
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class MetadataManager {
    
    private final Map<String, MetadataProvider<?>> providers = new ConcurrentHashMap<>();
    
    /**
     * 注册元数据提供者
     * 
     * @param provider 元数据提供者
     */
    public void registerProvider(MetadataProvider<?> provider) {
        String type = provider.getMetadataType();
        log.info("注册元数据提供者: {}", type);
        providers.put(type, provider);
    }
    
    /**
     * 获取元数据提供者
     * 
     * @param type 元数据类型
     * @return 元数据提供者
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<MetadataProvider<T>> getProvider(String type) {
        return Optional.ofNullable((MetadataProvider<T>) providers.get(type));
    }
    
    /**
     * 获取所有元数据提供者
     * 
     * @return 提供者列表
     */
    public List<MetadataProvider<?>> getAllProviders() {
        return List.copyOf(providers.values());
    }
    
    /**
     * 刷新所有元数据缓存
     * 
     * @return 刷新结果
     */
    public RefreshAllResult refreshAll() {
        log.info("刷新所有元数据缓存");
        
        int successCount = 0;
        int totalCount = providers.size();
        StringBuilder messages = new StringBuilder();
        
        for (MetadataProvider<?> provider : providers.values()) {
            try {
                MetadataProvider.RefreshResult result = provider.refresh();
                if (result.success()) {
                    successCount++;
                    messages.append(String.format("%s: 成功刷新%d项; ", 
                        provider.getMetadataType(), result.count()));
                } else {
                    messages.append(String.format("%s: 刷新失败 - %s; ", 
                        provider.getMetadataType(), result.message()));
                }
            } catch (Exception e) {
                log.error("刷新元数据提供者失败: {}", provider.getMetadataType(), e);
                messages.append(String.format("%s: 刷新异常 - %s; ", 
                    provider.getMetadataType(), e.getMessage()));
            }
        }
        
        boolean allSuccess = successCount == totalCount;
        String finalMessage = String.format("刷新完成: %d/%d 成功. %s", 
            successCount, totalCount, messages.toString());
        
        log.info(finalMessage);
        
        return new RefreshAllResult(
            allSuccess,
            successCount,
            totalCount,
            finalMessage,
            System.currentTimeMillis()
        );
    }
    
    /**
     * 刷新指定类型的元数据缓存
     * 
     * @param type 元数据类型
     * @return 刷新结果
     */
    public Optional<MetadataProvider.RefreshResult> refresh(String type) {
        log.info("刷新指定元数据缓存: {}", type);
        
        return getProvider(type)
                .map(MetadataProvider::refresh);
    }
    
    /**
     * 获取元数据状态信息
     * 
     * @return 状态信息
     */
    public List<MetadataStatus> getStatus() {
        return providers.values().stream()
                .map(provider -> new MetadataStatus(
                    provider.getMetadataType(),
                    provider.isLoaded(),
                    provider.getLoadTime(),
                    provider.getAll().size()
                ))
                .toList();
    }
    
    /**
     * 全量刷新结果
     */
    public record RefreshAllResult(
        boolean allSuccess,
        int successCount,
        int totalCount,
        String message,
        long timestamp
    ) {}
    
    /**
     * 元数据状态
     */
    public record MetadataStatus(
        String type,
        boolean loaded,
        long loadTime,
        int count
    ) {}
}
