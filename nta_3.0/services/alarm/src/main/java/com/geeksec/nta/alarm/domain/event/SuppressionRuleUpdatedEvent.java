package com.geeksec.nta.alarm.domain.event;

import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionRule;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * 抑制规则更新事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@RequiredArgsConstructor
public class SuppressionRuleUpdatedEvent {
    
    /**
     * 抑制规则ID
     */
    private final SuppressionId suppressionId;
    
    /**
     * 旧规则
     */
    private final SuppressionRule oldRule;
    
    /**
     * 新规则
     */
    private final SuppressionRule newRule;
    
    /**
     * 操作人
     */
    private final String operator;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt = LocalDateTime.now();
}
