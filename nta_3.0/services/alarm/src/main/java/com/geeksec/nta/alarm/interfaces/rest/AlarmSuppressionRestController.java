package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.nta.alarm.application.service.query.AlarmSuppressionQueryService;
import com.geeksec.nta.alarm.application.service.command.AlarmSuppressionCommandService;
import com.geeksec.nta.alarm.application.service.integration.AlarmSuppressionProviderService;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVoVo;
import com.geeksec.nta.alarm.interfaces.dto.request.CreateSuppressionRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.SuppressionQueryRequest;
import com.geeksec.nta.alarm.interfaces.dto.request.SuppressionCheckRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 告警抑制REST控制器
 * 负责告警抑制规则资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/suppressions")
@RequiredArgsConstructor
@Tag(name = "告警抑制", description = "告警抑制规则管理相关接口")
public class AlarmSuppressionRestController {
    
    private final AlarmSuppressionQueryService suppressionQueryService;
    private final AlarmSuppressionCommandService suppressionCommandService;
    private final AlarmSuppressionProviderService suppressionProviderService;
    
    // ==================== 抑制规则查询操作 ====================
    
    /**
     * 查询抑制规则列表
     */
    @GetMapping
    @Operation(summary = "查询抑制规则列表", description = "分页查询告警抑制规则列表")
    public ApiResponse<PageResultVo<AlarmSuppressionResponse>> getSuppressions(
            @Valid @ModelAttribute SuppressionQueryRequest request) {
        
        log.info("查询抑制规则列表: {}", request);
        
        var query = request.toQuery();
        var result = suppressionQueryService.querySuppressions(query);
        var pageResult = PageResultVo.of(result.getRecords(), result.getTotal(), 
                                     result.getCurrent(), result.getSize());
        
        return ApiResponse.success(pageResult, "查询成功");
    }
    
    /**
     * 查询抑制规则详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询抑制规则详情", description = "根据抑制规则ID查询详细信息")
    public ApiResponse<AlarmSuppressionResponse> getSuppression(
            @Parameter(description = "抑制规则ID") @PathVariable String id) {
        
        log.info("查询抑制规则详情: {}", id);
        
        var suppressionId = new com.geeksec.nta.alarm.domain.valueobject.SuppressionId(id);
        var suppression = suppressionQueryService.getSuppression(suppressionId);
        
        if (suppression.isPresent()) {
            return ApiResponse.success(suppression.get(), "查询成功");
        } else {
            return ApiResponse.notFound("抑制规则");
        }
    }
    
    /**
     * 获取活跃的抑制规则
     */
    @GetMapping("/active")
    @Operation(summary = "获取活跃抑制规则", description = "获取当前生效的抑制规则列表")
    public ApiResponse<List<AlarmSuppressionResponse>> getActiveSuppressions() {
        
        log.info("获取活跃抑制规则");
        
        var activeSuppressions = suppressionQueryService.getActiveSuppressions();
        
        return ApiResponse.success(activeSuppressions, "查询成功");
    }
    
    // ==================== 抑制规则管理操作 ====================
    
    /**
     * 创建抑制规则
     */
    @PostMapping
    @Operation(summary = "创建抑制规则", description = "创建新的告警抑制规则")
    @ResponseStatus(HttpStatus.CREATED)
    public ApiResponse<String> createSuppression(
            @Valid @RequestBody CreateSuppressionRequest request) {
        
        log.info("创建抑制规则: {}", request);
        
        var command = request.toCommand();
        var suppressionId = suppressionCommandService.createSuppression(command);
        
        return ApiResponse.success(suppressionId.value(), "抑制规则创建成功");
    }
    
    /**
     * 删除抑制规则
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除抑制规则", description = "删除指定的抑制规则")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public ApiResponse<Void> deleteSuppression(
            @Parameter(description = "抑制规则ID") @PathVariable String id,
            @Parameter(description = "操作人") @RequestParam String operator) {
        
        log.info("删除抑制规则: id={}, operator={}", id, operator);
        
        var command = new com.geeksec.nta.alarm.application.command.DeleteSuppressionCommand(
                new com.geeksec.nta.alarm.domain.valueobject.SuppressionId(id),
                operator,
                "手动删除"
        );
        
        boolean success = suppressionCommandService.deleteSuppression(command);
        
        if (success) {
            return ApiResponse.success("抑制规则删除成功");
        } else {
            return ApiResponse.businessError("抑制规则删除失败");
        }
    }
    
    /**
     * 批量删除抑制规则
     */
    @DeleteMapping
    @Operation(summary = "批量删除抑制规则", description = "根据条件批量删除抑制规则")
    public ApiResponse<Integer> batchDeleteSuppressions(
            @Parameter(description = "受害者IP") @RequestParam(required = false) String victim,
            @Parameter(description = "攻击者IP") @RequestParam(required = false) String attacker,
            @Parameter(description = "告警标签") @RequestParam(required = false) String label,
            @Parameter(description = "操作人") @RequestParam String operator) {
        
        log.info("批量删除抑制规则: victim={}, attacker={}, label={}, operator={}", 
                victim, attacker, label, operator);
        
        int deletedCount = suppressionCommandService.deleteSuppressionsByCondition(
                victim, attacker, label, operator);
        
        return ApiResponse.success(deletedCount, String.format("成功删除%d个抑制规则", deletedCount));
    }
    
    // ==================== 抑制规则提供操作（供flink-jobs调用） ====================

    /**
     * 获取活跃的抑制规则
     * 供flink-jobs/alarm-processor调用
     */
    @GetMapping("/rules/active")
    @Operation(summary = "获取活跃抑制规则", description = "获取当前生效的抑制规则，供flink-jobs调用")
    public ApiResponse<List<AlarmSuppressionProviderService.SuppressionRuleDto>> getActiveRules() {

        log.info("获取活跃抑制规则（供flink-jobs调用）");

        var rules = suppressionProviderService.getActiveSuppressionRules();

        return ApiResponse.success(rules, "查询成功");
    }

    /**
     * 根据条件查询抑制规则
     * 供flink-jobs/alarm-processor调用
     */
    @GetMapping("/rules/search")
    @Operation(summary = "根据条件查询抑制规则", description = "根据条件查询匹配的抑制规则，供flink-jobs调用")
    public ApiResponse<List<AlarmSuppressionProviderService.SuppressionRuleDto>> searchRules(
            @Parameter(description = "受害者IP") @RequestParam(required = false) String victim,
            @Parameter(description = "攻击者IP") @RequestParam(required = false) String attacker,
            @Parameter(description = "告警标签") @RequestParam(required = false) String label) {

        log.info("根据条件查询抑制规则: victim={}, attacker={}, label={}", victim, attacker, label);

        var rules = suppressionProviderService.getSuppressionRulesByCondition(victim, attacker, label);

        return ApiResponse.success(rules, "查询成功");
    }

    /**
     * 检查是否存在匹配的规则
     * 供flink-jobs/alarm-processor调用
     */
    @GetMapping("/rules/exists")
    @Operation(summary = "检查规则是否存在", description = "检查是否存在匹配的抑制规则，供flink-jobs调用")
    public ApiResponse<Boolean> hasMatchingRule(
            @Parameter(description = "受害者IP") @RequestParam String victim,
            @Parameter(description = "攻击者IP") @RequestParam String attacker,
            @Parameter(description = "告警标签") @RequestParam String label) {

        log.info("检查规则是否存在: victim={}, attacker={}, label={}", victim, attacker, label);

        boolean exists = suppressionProviderService.hasMatchingRule(victim, attacker, label);

        return ApiResponse.success(exists, "检查完成");
    }

    /**
     * 获取规则最后更新时间
     * 供flink-jobs/alarm-processor判断是否需要重新加载规则
     */
    @GetMapping("/rules/last-update")
    @Operation(summary = "获取规则最后更新时间", description = "获取抑制规则的最后更新时间，供flink-jobs判断是否需要重新加载")
    public ApiResponse<Long> getLastUpdateTime() {

        log.info("获取规则最后更新时间");

        long lastUpdateTime = suppressionProviderService.getLastUpdateTime();

        return ApiResponse.success(lastUpdateTime, "查询成功");
    }
    
    // ==================== 抑制统计操作 ====================

    /**
     * 获取抑制统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取抑制统计信息", description = "获取抑制规则的统计信息")
    public ApiResponse<AlarmSuppressionProviderService.SuppressionStatistics> getSuppressionStatistics() {

        log.info("获取抑制统计信息");

        var statistics = suppressionProviderService.getSuppressionStatistics();

        return ApiResponse.success(statistics, "统计查询成功");
    }
}
