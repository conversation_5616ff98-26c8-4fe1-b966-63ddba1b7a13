package com.geeksec.nta.alarm.domain.aggregate.subscription;

import com.mybatisflex.annotation.*;
import com.geeksec.nta.alarm.interfaces.dto.subscription.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警订阅实体
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Table(value = "alarm_subscription", comment = "告警订阅规则")
public class AlarmSubscription {
    
    @Id(keyType = KeyType.Generator)
    private String id;
    
    @Column("user_id")
    private String userId;
    
    @Column("subscription_name")
    private String subscriptionName;
    
    private String description;
    
    private Boolean enabled;
    
    @Column("priority_level")
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表 (JSON格式存储)
     */
    @Column("match_rules")
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 通知渠道配置 (JSON格式存储)
     */
    @Column("notification_channels")
    private List<NotificationChannelDto> notificationChannels;
    
    /**
     * 通知频率类型
     */
    @Column("frequency_type")
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置 (JSON格式存储)
     */
    @Column("frequency_config")
    private FrequencyConfigDto frequencyConfig;
    
    /**
     * 是否启用免打扰
     */
    @Column("quiet_hours_enabled")
    private Boolean quietHoursEnabled;
    
    /**
     * 免打扰时间配置 (JSON格式存储)
     */
    @Column("quiet_hours_config")
    private QuietHoursConfigDto quietHoursConfig;
    
    /**
     * 触发次数
     */
    @Column("trigger_count")
    private Integer triggerCount;
    
    /**
     * 最后触发时间
     */
    @Column("last_triggered_time")
    private LocalDateTime lastTriggeredTime;
    
    /**
     * 创建人
     */
    @Column("created_by")
    private String createdBy;
    
    /**
     * 创建时间
     */
    @Column("created_time")
    private LocalDateTime createdTime;
    
    /**
     * 更新人
     */
    @Column("updated_by")
    private String updatedBy;
    
    /**
     * 更新时间
     */
    @Column("updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 通知频率类型枚举
     */
    public enum FrequencyType {
        /** 实时通知 */
        REAL_TIME,
        /** 间隔通知 */
        INTERVAL,
        /** 批量通知 */
        BATCH,
        /** 摘要通知 */
        DIGEST
    }
}
