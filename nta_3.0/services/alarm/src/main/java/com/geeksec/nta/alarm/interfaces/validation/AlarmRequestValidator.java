package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 告警请求验证器
 * 提供自定义验证注解和验证逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
public class AlarmRequestValidator {
    
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");
    
    private static final Pattern ALARM_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,64}$");
    
    private static final List<String> VALID_ALARM_STATUSES = List.of(
            "PENDING", "PROCESSING", "RESOLVED", "IGNORED", "FALSE_POSITIVE");
    
    private static final List<String> VALID_SEVERITIES = List.of(
            "LOW", "MEDIUM", "HIGH", "CRITICAL");
    
    private static final List<String> VALID_SORT_FIELDS = List.of(
            "createTime", "updateTime", "severity", "alarmType", "status");
    
    /**
     * 验证IP地址格式
     */
    public static boolean isValidIpAddress(String ip) {
        return ip != null && IP_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 验证告警ID格式
     */
    public static boolean isValidAlarmId(String alarmId) {
        return alarmId != null && ALARM_ID_PATTERN.matcher(alarmId).matches();
    }
    
    /**
     * 验证告警状态
     */
    public static boolean isValidAlarmStatus(String status) {
        return status != null && VALID_ALARM_STATUSES.contains(status.toUpperCase());
    }
    
    /**
     * 验证严重程度
     */
    public static boolean isValidSeverity(String severity) {
        return severity != null && VALID_SEVERITIES.contains(severity.toUpperCase());
    }
    
    /**
     * 验证排序字段
     */
    public static boolean isValidSortField(String sortField) {
        return sortField != null && VALID_SORT_FIELDS.contains(sortField);
    }
    
    /**
     * 验证时间范围
     */
    public static boolean isValidTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return true; // 允许为空
        }
        return !startTime.isAfter(endTime);
    }
}
