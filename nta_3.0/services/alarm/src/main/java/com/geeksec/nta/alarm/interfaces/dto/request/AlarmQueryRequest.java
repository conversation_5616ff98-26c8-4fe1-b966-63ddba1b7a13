package com.geeksec.nta.alarm.interfaces.dto.request;

import com.geeksec.nta.alarm.application.query.AlarmListQuery;
import com.geeksec.common.dto.BaseQueryRequest;
// import com.geeksec.nta.alarm.interfaces.validation.ValidAlarmStatus;
// import com.geeksec.nta.alarm.interfaces.validation.ValidIpAddress;
// import com.geeksec.nta.alarm.interfaces.validation.ValidSeverity;
// import com.geeksec.nta.alarm.interfaces.validation.ValidTimeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警查询请求DTO
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
// @ValidTimeRange
public class AlarmQueryRequest extends BaseQueryRequest {
    
    /**
     * 告警类型列表
     */
    @Size(max = 50, message = "告警类型数量不能超过50个")
    private List<String> alarmTypes;
    
    /**
     * 告警状态列表
     */
    @Size(max = 10, message = "告警状态数量不能超过10个")
    private List<String> alarmStatuses;
    
    /**
     * 严重程度列表
     */
    @Size(max = 10, message = "严重程度数量不能超过10个")
    private List<String> severities;
    
    /**
     * 攻击者IP列表
     */
    @Size(max = 100, message = "攻击者IP数量不能超过100个")
    private List<String> attackerIps;
    
    /**
     * 受害者IP列表
     */
    @Size(max = 100, message = "受害者IP数量不能超过100个")
    private List<String> victimIps;
    
    /**
     * 是否包含已删除的告警
     */
    private Boolean includeDeleted = false;
    
    /**
     * 用户ID（用于权限过滤）
     */
    private String userId;
    
    /**
     * 转换为查询对象
     */
    public AlarmListQuery toQuery() {
        return AlarmListQuery.builder()
                .page(getPageNum())
                .size(getPageSize())
                .alarmTypes(alarmTypes)
                .alarmStatuses(alarmStatuses)
                .severities(severities)
                .attackerIps(attackerIps)
                .victimIps(victimIps)
                .startTime(getCreateTimeStart())
                .endTime(getCreateTimeEnd())
                .keyword(getKeyword())
                .sortField(getSortField())
                .sortDirection(getSortOrder())
                .includeDeleted(includeDeleted)
                .userId(userId)
                .build();
    }
}
