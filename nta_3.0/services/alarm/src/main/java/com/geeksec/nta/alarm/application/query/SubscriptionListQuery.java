package com.geeksec.nta.alarm.application.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订阅列表查询对象
 * 用于查询告警订阅信息
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionListQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 订阅名称（模糊查询）
     */
    private String subscriptionName;

    /**
     * 订阅类型
     */
    private String subscriptionType;

    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;

    /**
     * 威胁等级列表
     */
    private List<String> threatLevels;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    @Builder.Default
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    @Builder.Default
    private String sortField = "createTime";

    /**
     * 排序方向（ASC/DESC）
     */
    @Builder.Default
    private String sortDirection = "DESC";

    /**
     * 验证查询参数
     *
     * @return 验证结果
     */
    public boolean isValid() {
        if (pageNum == null || pageNum < 1) {
            return false;
        }
        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return false;
        }
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            return false;
        }
        return true;
    }

    /**
     * 获取偏移量
     *
     * @return 偏移量
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 是否有用户过滤条件
     *
     * @return 是否有用户过滤
     */
    public boolean hasUserFilter() {
        return userId != null && !userId.trim().isEmpty();
    }

    /**
     * 是否有名称过滤条件
     *
     * @return 是否有名称过滤
     */
    public boolean hasNameFilter() {
        return subscriptionName != null && !subscriptionName.trim().isEmpty();
    }

    /**
     * 是否有类型过滤条件
     *
     * @return 是否有类型过滤
     */
    public boolean hasTypeFilter() {
        return subscriptionType != null && !subscriptionType.trim().isEmpty();
    }

    /**
     * 是否有告警类型过滤条件
     *
     * @return 是否有告警类型过滤
     */
    public boolean hasAlarmTypesFilter() {
        return alarmTypes != null && !alarmTypes.isEmpty();
    }

    /**
     * 是否有威胁等级过滤条件
     *
     * @return 是否有威胁等级过滤
     */
    public boolean hasThreatLevelsFilter() {
        return threatLevels != null && !threatLevels.isEmpty();
    }

    /**
     * 是否有启用状态过滤条件
     *
     * @return 是否有启用状态过滤
     */
    public boolean hasEnabledFilter() {
        return enabled != null;
    }

    /**
     * 是否有时间范围过滤条件
     *
     * @return 是否有时间范围过滤
     */
    public boolean hasTimeRangeFilter() {
        return createTimeStart != null || createTimeEnd != null;
    }
}