package com.geeksec.nta.alarm.interfaces.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Set;

/**
 * 告警严重程度验证器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class ValidSeverityValidator implements ConstraintValidator<ValidSeverity, String> {
    
    private static final Set<String> VALID_SEVERITIES = Set.of(
        "LOW", "MEDIUM", "HIGH", "CRITICAL"
    );
    
    @Override
    public void initialize(ValidSeverity constraintAnnotation) {
        // 初始化方法，可以为空
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // null值由@NotNull等其他注解处理
        }
        return VALID_SEVERITIES.contains(value.toUpperCase());
    }
}