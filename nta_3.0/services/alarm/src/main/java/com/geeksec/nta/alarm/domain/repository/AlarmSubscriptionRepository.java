package com.geeksec.nta.alarm.domain.repository;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.domain.valueobject.UserId;
import com.geeksec.common.entity.PageResultVo;

import java.util.List;
import java.util.Optional;

/**
 * 告警订阅聚合根仓储接口
 * 定义订阅聚合的持久化操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSubscriptionRepository {
    
    /**
     * 保存订阅
     * 
     * @param subscription 订阅聚合根
     * @return 保存后的订阅
     */
    AlarmSubscription save(AlarmSubscription subscription);
    
    /**
     * 根据ID查找订阅
     * 
     * @param subscriptionId 订阅ID
     * @return 订阅聚合根
     */
    Optional<AlarmSubscription> findById(SubscriptionId subscriptionId);
    
    /**
     * 根据用户ID和订阅ID查找订阅
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @return 订阅聚合根
     */
    Optional<AlarmSubscription> findByIdAndUserId(SubscriptionId subscriptionId, UserId userId);
    
    /**
     * 删除订阅
     * 
     * @param subscriptionId 订阅ID
     * @return 是否删除成功
     */
    boolean deleteById(SubscriptionId subscriptionId);
    
    /**
     * 分页查询用户订阅
     * 
     * @param userId 用户ID
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<AlarmSubscription> findByUserIdAndPage(UserId userId, SubscriptionQuery query);
    
    /**
     * 查找用户的所有订阅
     * 
     * @param userId 用户ID
     * @return 订阅列表
     */
    List<AlarmSubscription> findByUserId(UserId userId);
    
    /**
     * 查找活跃的订阅
     * 
     * @return 活跃订阅列表
     */
    List<AlarmSubscription> findActiveSubscriptions();
    
    /**
     * 检查订阅名称是否已被使用
     * 
     * @param subscriptionName 订阅名称
     * @param userId 用户ID
     * @param excludeId 排除的订阅ID
     * @return 是否已被使用
     */
    boolean isSubscriptionNameUsed(String subscriptionName, UserId userId, SubscriptionId excludeId);
    
    /**
     * 统计用户订阅数量
     * 
     * @param userId 用户ID
     * @return 订阅数量
     */
    long countByUserId(UserId userId);
    
    /**
     * 统计用户活跃订阅数量
     * 
     * @param userId 用户ID
     * @return 活跃订阅数量
     */
    long countActiveByUserId(UserId userId);
    
    /**
     * 检查订阅是否存在
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean existsByIdAndUserId(SubscriptionId subscriptionId, UserId userId);
    
    /**
     * 订阅查询条件
     */
    interface SubscriptionQuery {
        // 查询条件接口，具体实现在application层
    }
}
