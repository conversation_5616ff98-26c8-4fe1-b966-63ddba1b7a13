package com.geeksec.nta.alarm.infrastructure.converter;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;
import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmTypeEnum;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警转换器
 * 负责在不同层之间转换告警对象
 *
 * <AUTHOR>
 */
@Component
public class AlarmConverter {

    /**
     * 将告警实体转换为列表响应对象
     *
     * @param alarm 告警实体
     * @return 告警列表响应对象
     */
    public AlarmListResponse toListResponse(Alarm alarm) {
        if (alarm == null) {
            return null;
        }

        return AlarmListResponse.builder()
                .alarmId(alarm.getId())
                .title(alarm.getIndex()) // 使用 index 作为标题
                .alarmType(convertToAlarmTypeEnum(alarm.getType()))
                .threatLevel(null) // 需要根据 score 计算威胁等级
                .status(AlarmHandlingStatus.fromCode(alarm.getStatus()))
                .createTime(convertToLocalDateTime(alarm.getCreateTime()))
                .updateTime(alarm.getHandleTime())
                .attackerIp(null) // 从 attackers 列表中获取
                .victimIp(null) // 从 victims 列表中获取
                .sourceType(null) // 从 source 对象中获取
                .reasonCode(null) // 从 reasons 列表中获取
                .reasonMessage(null) // 从 reasons 列表中获取
                .handlerName(alarm.getHandler())
                .handlerId(null) // 需要额外字段
                .handleTime(alarm.getHandleTime())
                .attributes(null) // 需要额外字段
                .score((double) alarm.getScore())
                .isRead(false) // 需要额外字段
                .build();
    }

    /**
     * 将告警实体转换为详情响应对象
     *
     * @param alarm 告警实体
     * @return 告警详情响应对象
     */
    public AlarmDetailResponse toDetailResponse(Alarm alarm) {
        if (alarm == null) {
            return null;
        }

        return AlarmDetailResponse.builder()
                .alarmId(alarm.getId())
                .title(alarm.getIndex()) // 使用 index 作为标题
                .description(alarm.getHandleNote()) // 使用处理备注作为描述
                .alarmType(convertToAlarmTypeEnum(alarm.getType()))
                .threatLevel(null) // 需要根据 score 计算威胁等级
                .status(AlarmHandlingStatus.fromCode(alarm.getStatus()))
                .createTime(convertToLocalDateTime(alarm.getCreateTime()))
                .updateTime(alarm.getHandleTime())
                .build();
    }

    /**
     * 批量转换告警实体为列表响应对象
     *
     * @param alarms 告警实体列表
     * @return 告警列表响应对象列表
     */
    public List<AlarmListResponse> toListResponse(List<Alarm> alarms) {
        if (alarms == null || alarms.isEmpty()) {
            return List.of();
        }

        return alarms.stream()
                .map(this::toListResponse)
                .collect(Collectors.toList());
    }

    /**
     * 批量转换告警实体为详情响应对象
     *
     * @param alarms 告警实体列表
     * @return 告警详情响应对象列表
     */
    public List<AlarmDetailResponse> toDetailResponse(List<Alarm> alarms) {
        if (alarms == null || alarms.isEmpty()) {
            return List.of();
        }

        return alarms.stream()
                .map(this::toDetailResponse)
                .collect(Collectors.toList());
    }

    /**
     * 将字符串转换为告警类型枚举
     *
     * @param type 告警类型字符串
     * @return 告警类型枚举
     */
    private AlarmTypeEnum convertToAlarmTypeEnum(String type) {
        if (type == null) {
            return null;
        }
        
        // 根据字符串匹配枚举值
        for (AlarmTypeEnum alarmType : AlarmTypeEnum.values()) {
            if (alarmType.getAlarmType().equals(type) || alarmType.name().equals(type)) {
                return alarmType;
            }
        }
        
        // 默认返回 MODEL
        return AlarmTypeEnum.MODEL;
    }

    /**
     * 将 Date 转换为 LocalDateTime
     *
     * @param date Date 对象
     * @return LocalDateTime 对象
     */
    private LocalDateTime convertToLocalDateTime(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime();
    }
}