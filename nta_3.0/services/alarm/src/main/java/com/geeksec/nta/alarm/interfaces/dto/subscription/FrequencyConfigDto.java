package com.geeksec.nta.alarm.interfaces.dto.subscription;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 频率配置 DTO
 * 用于配置告警通知的频率控制
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrequencyConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 间隔时间（分钟）
     * 用于 INTERVAL 类型
     */
    private Integer intervalMinutes;

    /**
     * 批量大小
     * 用于 BATCH 类型
     */
    private Integer batchSize;

    /**
     * 批量时间窗口（分钟）
     * 用于 BATCH 类型
     */
    private Integer batchWindowMinutes;

    /**
     * 摘要发送时间列表
     * 用于 DIGEST 类型，格式：HH:mm
     */
    private List<String> digestTimes;

    /**
     * 摘要发送的星期几
     * 用于 DIGEST 类型，1-7 表示周一到周日
     */
    private List<Integer> digestDaysOfWeek;

    /**
     * 最大通知次数（每天）
     */
    private Integer maxNotificationsPerDay;

    /**
     * 最大通知次数（每小时）
     */
    private Integer maxNotificationsPerHour;

    /**
     * 是否启用去重
     */
    @Builder.Default
    private Boolean enableDeduplication = true;

    /**
     * 去重时间窗口（分钟）
     */
    @Builder.Default
    private Integer deduplicationWindowMinutes = 60;

    /**
     * 去重字段列表
     * 用于判断告警是否重复
     */
    private List<String> deduplicationFields;

    /**
     * 是否启用升级通知
     */
    @Builder.Default
    private Boolean enableEscalation = false;

    /**
     * 升级时间（分钟）
     * 超过此时间未处理则升级通知
     */
    private Integer escalationMinutes;

    /**
     * 升级通知渠道列表
     */
    private List<String> escalationChannels;

    /**
     * 验证配置是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        // 验证间隔配置
        if (intervalMinutes != null && intervalMinutes <= 0) {
            return false;
        }
        
        // 验证批量配置
        if (batchSize != null && batchSize <= 0) {
            return false;
        }
        if (batchWindowMinutes != null && batchWindowMinutes <= 0) {
            return false;
        }
        
        // 验证摘要时间格式
        if (digestTimes != null) {
            for (String timeStr : digestTimes) {
                if (!isValidTimeFormat(timeStr)) {
                    return false;
                }
            }
        }
        
        // 验证星期几
        if (digestDaysOfWeek != null) {
            for (Integer day : digestDaysOfWeek) {
                if (day < 1 || day > 7) {
                    return false;
                }
            }
        }
        
        // 验证限制配置
        if (maxNotificationsPerDay != null && maxNotificationsPerDay <= 0) {
            return false;
        }
        if (maxNotificationsPerHour != null && maxNotificationsPerHour <= 0) {
            return false;
        }
        
        // 验证去重配置
        if (deduplicationWindowMinutes != null && deduplicationWindowMinutes <= 0) {
            return false;
        }
        
        // 验证升级配置
        if (enableEscalation && escalationMinutes != null && escalationMinutes <= 0) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证时间格式是否正确（HH:mm）
     *
     * @param timeStr 时间字符串
     * @return 是否有效
     */
    private boolean isValidTimeFormat(String timeStr) {
        try {
            LocalTime.parse(timeStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取摘要时间的 LocalTime 列表
     *
     * @return LocalTime 列表
     */
    public List<LocalTime> getDigestLocalTimes() {
        if (digestTimes == null) {
            return null;
        }
        return digestTimes.stream()
                .map(LocalTime::parse)
                .toList();
    }

    /**
     * 是否启用频率限制
     *
     * @return 是否启用
     */
    public boolean hasFrequencyLimit() {
        return maxNotificationsPerDay != null || maxNotificationsPerHour != null;
    }

    /**
     * 是否配置了去重
     *
     * @return 是否配置
     */
    public boolean hasDeduplication() {
        return enableDeduplication && deduplicationFields != null && !deduplicationFields.isEmpty();
    }

    /**
     * 是否配置了升级通知
     *
     * @return 是否配置
     */
    public boolean hasEscalation() {
        return enableEscalation && escalationMinutes != null && 
               escalationChannels != null && !escalationChannels.isEmpty();
    }

    /**
     * 获取默认的实时通知配置
     *
     * @return 实时通知配置
     */
    public static FrequencyConfigDto realTimeConfig() {
        return FrequencyConfigDto.builder()
                .enableDeduplication(true)
                .deduplicationWindowMinutes(5)
                .build();
    }

    /**
     * 获取默认的间隔通知配置
     *
     * @param intervalMinutes 间隔分钟数
     * @return 间隔通知配置
     */
    public static FrequencyConfigDto intervalConfig(int intervalMinutes) {
        return FrequencyConfigDto.builder()
                .intervalMinutes(intervalMinutes)
                .enableDeduplication(true)
                .deduplicationWindowMinutes(intervalMinutes)
                .build();
    }

    /**
     * 获取默认的批量通知配置
     *
     * @param batchSize 批量大小
     * @param windowMinutes 时间窗口
     * @return 批量通知配置
     */
    public static FrequencyConfigDto batchConfig(int batchSize, int windowMinutes) {
        return FrequencyConfigDto.builder()
                .batchSize(batchSize)
                .batchWindowMinutes(windowMinutes)
                .enableDeduplication(true)
                .deduplicationWindowMinutes(windowMinutes)
                .build();
    }
}