package com.geeksec.nta.alarm.interfaces.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 测试订阅结果 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestSubscriptionResult {
    
    /**
     * 是否匹配
     */
    private Boolean matched;
    
    /**
     * 匹配的规则数量
     */
    private Integer matchedRulesCount;
    
    /**
     * 总规则数量
     */
    private Integer totalRulesCount;
    
    /**
     * 规则匹配详情
     */
    private List<RuleMatchDetail> ruleDetails;
    
    /**
     * 规则匹配详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleMatchDetail {
        
        /**
         * 规则描述
         */
        private String ruleDescription;
        
        /**
         * 字段名称
         */
        private String fieldName;
        
        /**
         * 操作符
         */
        private String operator;
        
        /**
         * 期望值
         */
        private String expectedValue;
        
        /**
         * 实际值
         */
        private String actualValue;
        
        /**
         * 是否匹配
         */
        private Boolean matched;
        
        /**
         * 匹配说明
         */
        private String message;
    }
    
    /**
     * 创建成功结果
     */
    public static TestSubscriptionResult success(int matchedCount, int totalCount, List<RuleMatchDetail> details) {
        return TestSubscriptionResult.builder()
                .matched(matchedCount == totalCount)
                .matchedRulesCount(matchedCount)
                .totalRulesCount(totalCount)
                .ruleDetails(details)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static TestSubscriptionResult failure(int matchedCount, int totalCount, List<RuleMatchDetail> details) {
        return TestSubscriptionResult.builder()
                .matched(false)
                .matchedRulesCount(matchedCount)
                .totalRulesCount(totalCount)
                .ruleDetails(details)
                .build();
    }
}
