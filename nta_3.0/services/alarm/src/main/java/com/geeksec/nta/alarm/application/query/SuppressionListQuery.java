package com.geeksec.nta.alarm.application.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 抑制规则列表查询对象
 * 用于查询告警抑制规则信息
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuppressionListQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则名称（模糊查询）
     */
    private String ruleName;

    /**
     * 抑制类型
     */
    private String suppressionType;

    /**
     * 抑制动作
     */
    private String action;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 优先级范围 - 最小值
     */
    private Integer minPriority;

    /**
     * 优先级范围 - 最大值
     */
    private Integer maxPriority;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 最后触发时间开始
     */
    private LocalDateTime lastTriggeredTimeStart;

    /**
     * 最后触发时间结束
     */
    private LocalDateTime lastTriggeredTimeEnd;

    /**
     * 触发次数范围 - 最小值
     */
    private Integer minTriggerCount;

    /**
     * 触发次数范围 - 最大值
     */
    private Integer maxTriggerCount;

    /**
     * 抑制告警数量范围 - 最小值
     */
    private Integer minSuppressedCount;

    /**
     * 抑制告警数量范围 - 最大值
     */
    private Integer maxSuppressedCount;

    /**
     * 标签过滤
     */
    private List<String> tags;

    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    @Builder.Default
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    @Builder.Default
    private String sortField = "createTime";

    /**
     * 排序方向（ASC/DESC）
     */
    @Builder.Default
    private String sortDirection = "DESC";

    /**
     * 是否包含统计信息
     */
    @Builder.Default
    private Boolean includeStats = false;

    /**
     * 验证查询参数
     *
     * @return 验证结果
     */
    public boolean isValid() {
        if (pageNum == null || pageNum < 1) {
            return false;
        }
        if (pageSize == null || pageSize < 1 || pageSize > 1000) {
            return false;
        }
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            return false;
        }
        if (lastTriggeredTimeStart != null && lastTriggeredTimeEnd != null && 
            lastTriggeredTimeStart.isAfter(lastTriggeredTimeEnd)) {
            return false;
        }
        if (minPriority != null && maxPriority != null && minPriority > maxPriority) {
            return false;
        }
        if (minTriggerCount != null && maxTriggerCount != null && minTriggerCount > maxTriggerCount) {
            return false;
        }
        if (minSuppressedCount != null && maxSuppressedCount != null && minSuppressedCount > maxSuppressedCount) {
            return false;
        }
        return true;
    }

    /**
     * 获取偏移量
     *
     * @return 偏移量
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 是否有规则名称过滤条件
     *
     * @return 是否有名称过滤
     */
    public boolean hasRuleNameFilter() {
        return ruleName != null && !ruleName.trim().isEmpty();
    }

    /**
     * 是否有类型过滤条件
     *
     * @return 是否有类型过滤
     */
    public boolean hasTypeFilter() {
        return suppressionType != null && !suppressionType.trim().isEmpty();
    }

    /**
     * 是否有动作过滤条件
     *
     * @return 是否有动作过滤
     */
    public boolean hasActionFilter() {
        return action != null && !action.trim().isEmpty();
    }

    /**
     * 是否有启用状态过滤条件
     *
     * @return 是否有启用状态过滤
     */
    public boolean hasEnabledFilter() {
        return enabled != null;
    }

    /**
     * 是否有优先级范围过滤条件
     *
     * @return 是否有优先级过滤
     */
    public boolean hasPriorityRangeFilter() {
        return minPriority != null || maxPriority != null;
    }

    /**
     * 是否有创建人过滤条件
     *
     * @return 是否有创建人过滤
     */
    public boolean hasCreatedByFilter() {
        return createdBy != null && !createdBy.trim().isEmpty();
    }

    /**
     * 是否有创建时间范围过滤条件
     *
     * @return 是否有创建时间过滤
     */
    public boolean hasCreateTimeRangeFilter() {
        return createTimeStart != null || createTimeEnd != null;
    }

    /**
     * 是否有最后触发时间范围过滤条件
     *
     * @return 是否有最后触发时间过滤
     */
    public boolean hasLastTriggeredTimeRangeFilter() {
        return lastTriggeredTimeStart != null || lastTriggeredTimeEnd != null;
    }

    /**
     * 是否有触发次数范围过滤条件
     *
     * @return 是否有触发次数过滤
     */
    public boolean hasTriggerCountRangeFilter() {
        return minTriggerCount != null || maxTriggerCount != null;
    }

    /**
     * 是否有抑制数量范围过滤条件
     *
     * @return 是否有抑制数量过滤
     */
    public boolean hasSuppressedCountRangeFilter() {
        return minSuppressedCount != null || maxSuppressedCount != null;
    }

    /**
     * 是否有标签过滤条件
     *
     * @return 是否有标签过滤
     */
    public boolean hasTagsFilter() {
        return tags != null && !tags.isEmpty();
    }

    /**
     * 获取有效的排序字段
     *
     * @return 排序字段
     */
    public String getValidSortField() {
        if (sortField == null || sortField.trim().isEmpty()) {
            return "createTime";
        }
        
        // 验证排序字段是否合法
        List<String> validFields = List.of(
            "createTime", "updateTime", "ruleName", "priority", 
            "triggerCount", "suppressedAlarmCount", "lastTriggeredTime"
        );
        
        return validFields.contains(sortField) ? sortField : "createTime";
    }

    /**
     * 获取有效的排序方向
     *
     * @return 排序方向
     */
    public String getValidSortDirection() {
        if (sortDirection == null || sortDirection.trim().isEmpty()) {
            return "DESC";
        }
        return "ASC".equalsIgnoreCase(sortDirection) ? "ASC" : "DESC";
    }

    /**
     * 创建默认查询对象
     *
     * @return 默认查询对象
     */
    public static SuppressionListQuery defaultQuery() {
        return SuppressionListQuery.builder()
                .pageNum(1)
                .pageSize(20)
                .sortField("createTime")
                .sortDirection("DESC")
                .includeStats(false)
                .build();
    }

    /**
     * 创建启用规则查询对象
     *
     * @return 启用规则查询对象
     */
    public static SuppressionListQuery enabledRulesQuery() {
        return SuppressionListQuery.builder()
                .enabled(true)
                .pageNum(1)
                .pageSize(20)
                .sortField("priority")
                .sortDirection("DESC")
                .includeStats(true)
                .build();
    }
}