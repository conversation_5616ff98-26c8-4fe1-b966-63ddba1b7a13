package com.geeksec.nta.alarm.application.service.query.impl;

import com.geeksec.common.enums.AlarmTarget;
import com.geeksec.common.enums.AlarmEntityRole;
import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.nta.alarm.application.service.query.AlarmMetadataApplicationService;
import com.geeksec.nta.alarm.domain.service.MetadataManager;
import com.geeksec.nta.alarm.domain.valueobject.AlarmType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 告警元数据应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmMetadataApplicationServiceImpl implements AlarmMetadataApplicationService {
    
    private final MetadataManager metadataManager;
    
    @Override
    public List<AlarmTarget> getAllAlarmTargets() {
        log.debug("获取所有告警目标");
        return Arrays.asList(AlarmTarget.values());
    }
    
    @Override
    public Optional<AlarmTarget> getAlarmTargetByCode(int code) {
        log.debug("根据代码获取告警目标: {}", code);
        return Arrays.stream(AlarmTarget.values())
                .filter(target -> target.getCode() == code)
                .findFirst();
    }
    
    @Override
    public List<AlarmEntityRole> getAllAlarmEntityRoles() {
        log.debug("获取所有告警实体角色");
        return Arrays.asList(AlarmEntityRole.values());
    }
    
    @Override
    public Optional<AlarmEntityRole> getAlarmEntityRoleByCode(int code) {
        log.debug("根据代码获取告警实体角色: {}", code);
        return Arrays.stream(AlarmEntityRole.values())
                .filter(role -> role.getCode() == code)
                .findFirst();
    }
    
    @Override
    public List<AlarmHandlingStatus> getAllAlarmHandlingStatuses() {
        log.debug("获取所有告警处理状态");
        return Arrays.asList(AlarmHandlingStatus.values());
    }
    
    @Override
    public Optional<AlarmHandlingStatus> getAlarmHandlingStatusByCode(int code) {
        log.debug("根据代码获取告警处理状态: {}", code);
        return Arrays.stream(AlarmHandlingStatus.values())
                .filter(status -> status.getCode() == code)
                .findFirst();
    }
    
    @Override
    public List<AlarmType> getAllAlarmTypes() {
        log.debug("获取所有告警类型");
        return metadataManager.getAllAlarmTypes();
    }
    
    @Override
    public Optional<AlarmType> getAlarmTypeByCode(String code) {
        log.debug("根据代码获取告警类型: {}", code);
        return metadataManager.getAlarmTypeByCode(code);
    }
    
    @Override
    public Map<String, String> getAlarmTypeMapping() {
        log.debug("获取告警类型映射");
        return metadataManager.getAlarmTypeMapping();
    }
    
    @Override
    public Map<String, String> getAllAlarmTargetTypes() {
        log.debug("获取所有告警目标类型");
        return Arrays.stream(AlarmTarget.values())
                .collect(Collectors.toMap(
                    target -> String.valueOf(target.getCode()),
                    AlarmTarget::getName
                ));
    }
    
    @Override
    public Optional<String> getAlarmTargetTypeByCode(String code) {
        log.debug("根据代码获取告警目标类型: {}", code);
        try {
            int codeInt = Integer.parseInt(code);
            return getAlarmTargetByCode(codeInt).map(AlarmTarget::getName);
        } catch (NumberFormatException e) {
            log.warn("无效的告警目标类型代码: {}", code);
            return Optional.empty();
        }
    }
    
    @Override
    public Map<String, String> getAllAlarmTargetBelongs() {
        log.debug("获取所有告警目标归属");
        // 这里可以根据实际业务需求实现
        return Map.of();
    }
    
    @Override
    public Optional<String> getAlarmTargetBelongByCode(String code) {
        log.debug("根据代码获取告警目标归属: {}", code);
        return getAllAlarmTargetBelongs().entrySet().stream()
                .filter(entry -> entry.getKey().equals(code))
                .map(Map.Entry::getValue)
                .findFirst();
    }
    
    @Override
    public List<Map<String, Object>> getAttackChainModel() {
        log.debug("获取攻击链模型");
        return Arrays.stream(CyberKillChain.values())
                .map(stage -> Map.<String, Object>of(
                    "stage", stage.name(),
                    "stageName", stage.getDisplayName(),
                    "description", stage.getDescription(),
                    "order", stage.ordinal()
                ))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CyberKillChain> getAllCyberKillChainStages() {
        log.debug("获取所有网络杀伤链阶段");
        return Arrays.asList(CyberKillChain.values());
    }
    
    @Override
    public Map<String, Object> getCyberKillChainDetails(CyberKillChain stage) {
        log.debug("获取网络杀伤链阶段详情: {}", stage);
        return Map.of(
            "stage", stage.name(),
            "stageName", stage.getDisplayName(),
            "description", stage.getDescription(),
            "order", stage.ordinal()
        );
    }
    
    @Override
    public MetadataManager.RefreshAllResult clearAlarmCache() {
        log.info("清除告警元数据缓存");
        return metadataManager.refreshAll();
    }
    
    @Override
    public MetadataManager.RefreshAllResult initAlarmCache() {
        log.info("初始化告警元数据缓存");
        return metadataManager.refreshAll();
    }
    
    @Override
    public List<MetadataManager.MetadataStatus> getMetadataStatus() {
        log.debug("获取元数据状态信息");
        return metadataManager.getMetadataStatus();
    }
    
    @Override
    public Optional<MetadataManager.RefreshAllResult> refreshMetadata(String metadataType) {
        log.info("刷新指定类型的元数据: {}", metadataType);
        try {
            MetadataManager.RefreshAllResult result = metadataManager.refreshAll();
            return Optional.of(result);
        } catch (Exception e) {
            log.error("刷新元数据失败: {}", metadataType, e);
            return Optional.empty();
        }
    }
}
