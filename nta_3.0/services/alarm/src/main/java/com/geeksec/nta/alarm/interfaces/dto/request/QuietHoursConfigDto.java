package com.geeksec.nta.alarm.interfaces.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

/**
 * 免打扰时间配置 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuietHoursConfigDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 开始时间
     */
    private LocalTime startTime;
    
    /**
     * 结束时间
     */
    private LocalTime endTime;
    
    /**
     * 生效的星期几（1-7，1为周一）
     */
    private List<Integer> weekdays;
    
    /**
     * 紧急告警是否忽略免打扰
     */
    private Boolean urgentIgnoreQuiet = true;
    
    /**
     * 创建工作时间外免打扰配置（晚上10点到早上8点）
     */
    public static QuietHoursConfigDto createNightQuietHours() {
        return QuietHoursConfigDto.builder()
                .startTime(LocalTime.of(22, 0))
                .endTime(LocalTime.of(8, 0))
                .weekdays(List.of(1, 2, 3, 4, 5, 6, 7)) // 每天
                .urgentIgnoreQuiet(true)
                .build();
    }
    
    /**
     * 创建周末免打扰配置
     */
    public static QuietHoursConfigDto createWeekendQuietHours() {
        return QuietHoursConfigDto.builder()
                .startTime(LocalTime.of(0, 0))
                .endTime(LocalTime.of(23, 59))
                .weekdays(List.of(6, 7)) // 周六、周日
                .urgentIgnoreQuiet(true)
                .build();
    }
}
