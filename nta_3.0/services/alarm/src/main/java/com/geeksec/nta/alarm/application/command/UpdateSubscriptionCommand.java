package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription.FrequencyType;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.interfaces.dto.request.FrequencyConfigDto;
import com.geeksec.nta.alarm.interfaces.dto.request.NotificationChannelDto;
import com.geeksec.nta.alarm.interfaces.dto.request.QuietHoursConfigDto;
import com.geeksec.nta.alarm.interfaces.dto.request.SubscriptionRuleDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 更新订阅命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSubscriptionCommand {
    
    /**
     * 订阅ID
     */
    private SubscriptionId subscriptionId;
    
    /**
     * 订阅名称
     */
    private String subscriptionName;
    
    /**
     * 订阅描述
     */
    private String description;
    
    /**
     * 优先级（1-5）
     */
    private Integer priorityLevel;
    
    /**
     * 匹配规则列表
     */
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 通知渠道列表
     */
    private List<NotificationChannelDto> notificationChannels;
    
    /**
     * 通知频率类型
     */
    private FrequencyType frequencyType;
    
    /**
     * 频率控制配置
     */
    private FrequencyConfigDto frequencyConfig;
    
    /**
     * 是否启用免打扰
     */
    private Boolean quietHoursEnabled;
    
    /**
     * 免打扰时间配置
     */
    private QuietHoursConfigDto quietHoursConfig;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (subscriptionId == null) {
            throw new IllegalArgumentException("订阅ID不能为空");
        }
        if (subscriptionName == null || subscriptionName.trim().isEmpty()) {
            throw new IllegalArgumentException("订阅名称不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
    }
}