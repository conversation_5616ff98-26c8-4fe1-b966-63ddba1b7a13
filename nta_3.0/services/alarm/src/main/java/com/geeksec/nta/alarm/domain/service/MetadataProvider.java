package com.geeksec.nta.alarm.domain.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 元数据提供者接口
 * 定义元数据获取的通用契约，支持不同类型的元数据源
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface MetadataProvider<T> {
    
    /**
     * 获取元数据类型
     * 
     * @return 元数据类型标识
     */
    String getMetadataType();
    
    /**
     * 获取所有元数据
     * 
     * @return 元数据列表
     */
    List<T> getAll();
    
    /**
     * 根据代码获取元数据
     * 
     * @param code 代码
     * @return 元数据对象
     */
    Optional<T> getByCode(String code);
    
    /**
     * 获取元数据映射
     * 
     * @return 代码到名称的映射
     */
    Map<String, String> getMapping();
    
    /**
     * 刷新元数据缓存
     * 
     * @return 刷新结果
     */
    RefreshResult refresh();
    
    /**
     * 检查元数据是否已加载
     * 
     * @return 是否已加载
     */
    boolean isLoaded();
    
    /**
     * 获取元数据加载时间
     * 
     * @return 加载时间戳
     */
    long getLoadTime();
    
    /**
     * 刷新结果
     */
    record RefreshResult(
        boolean success,
        int count,
        String message,
        long timestamp
    ) {}
}
