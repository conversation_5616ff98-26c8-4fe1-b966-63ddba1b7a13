package com.geeksec.nta.alarm.application.service;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import com.geeksec.nta.alarm.interfaces.dto.suppression.AlarmSuppressionResponse;

/**
 * 告警抑制查询服务接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionQueryService {

    /**
     * 分页查询告警抑制规则
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<AlarmSuppressionResponse> listSuppressions(SuppressionListQuery query);

    /**
     * 根据ID查询告警抑制规则
     *
     * @param suppressionId 抑制规则ID
     * @return 抑制规则详情
     */
    AlarmSuppressionResponse getSuppressionById(Long suppressionId);
}