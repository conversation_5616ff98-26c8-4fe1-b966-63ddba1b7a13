package com.geeksec.nta.alarm.interfaces.rest;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.enums.AlarmEntityRole;
import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.common.enums.AlarmTarget;
import com.geeksec.common.enums.CyberKillChain;
import com.geeksec.nta.alarm.application.service.query.AlarmMetadataApplicationService;
import com.geeksec.nta.alarm.domain.service.MetadataManager;
import com.geeksec.nta.alarm.domain.entity.AlarmType;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警元数据REST控制器
 * 负责告警元数据资源的RESTful API
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata")
@RequiredArgsConstructor
@Tag(name = "告警元数据", description = "告警元数据查询相关接口")
public class AlarmMetadataRestController {
    
    private final AlarmMetadataApplicationService metadataService;
    
    // ==================== 告警类型相关 ====================
    
    /**
     * 获取所有告警类型
     */
    @GetMapping("/alarm-types")
    @Operation(summary = "获取告警类型", description = "获取所有告警类型列表")
    public ApiResponse<List<AlarmType>> getAllAlarmTypes() {
        
        log.info("获取所有告警类型");
        
        var alarmTypes = metadataService.getAllAlarmTypes();
        
        return ApiResponse.success(alarmTypes, "查询成功");
    }
    
    /**
     * 根据代码获取告警类型
     */
    @GetMapping("/alarm-types/{code}")
    @Operation(summary = "根据代码获取告警类型", description = "根据告警类型代码获取详细信息")
    public ApiResponse<AlarmType> getAlarmTypeByCode(
            @Parameter(description = "告警类型代码") @PathVariable String code) {
        
        log.info("根据代码获取告警类型: {}", code);
        
        var alarmType = metadataService.getAlarmTypeByCode(code);
        
        if (alarmType.isPresent()) {
            return ApiResponse.success("查询成功", alarmType.get());
        } else {
            return ApiResponse.notFound("告警类型");
        }
    }
    
    /**
     * 获取告警类型映射
     */
    @GetMapping("/alarm-types/mapping")
    @Operation(summary = "获取告警类型映射", description = "获取告警类型代码到名称的映射")
    public ApiResponse<Map<String, String>> getAlarmTypeMapping() {
        
        log.info("获取告警类型映射");
        
        var mapping = metadataService.getAlarmTypeMapping();
        
        return ApiResponse.success("查询成功", mapping);
    }
    
    // ==================== 告警目标相关 ====================
    
    /**
     * 获取所有告警目标
     */
    @GetMapping("/alarm-targets")
    @Operation(summary = "获取告警目标", description = "获取所有告警目标列表")
    public ApiResponse<List<AlarmTarget>> getAllAlarmTargets() {
        
        log.info("获取所有告警目标");
        
        var alarmTargets = metadataService.getAllAlarmTargets();
        
        return ApiResponse.success("查询成功", alarmTargets);
    }
    
    /**
     * 根据代码获取告警目标
     */
    @GetMapping("/alarm-targets/{code}")
    @Operation(summary = "根据代码获取告警目标", description = "根据告警目标代码获取详细信息")
    public ApiResponse<AlarmTarget> getAlarmTargetByCode(
            @Parameter(description = "告警目标代码") @PathVariable int code) {
        
        log.info("根据代码获取告警目标: {}", code);
        
        var alarmTarget = metadataService.getAlarmTargetByCode(code);
        
        if (alarmTarget.isPresent()) {
            return ApiResponse.success("查询成功", alarmTarget.get());
        } else {
            return ApiResponse.notFound("告警目标");
        }
    }
    
    // ==================== 告警状态相关 ====================
    
    /**
     * 获取所有告警状态
     */
    @GetMapping("/alarm-statuses")
    @Operation(summary = "获取告警状态", description = "获取所有告警处理状态列表")
    public ApiResponse<List<AlarmHandlingStatus>> getAllAlarmStates() {
        
        log.info("获取所有告警状态");
        
        var alarmStates = metadataService.getAllAlarmStates();
        
        return ApiResponse.success("查询成功", alarmStates);
    }
    
    /**
     * 根据代码获取告警状态
     */
    @GetMapping("/alarm-statuses/{code}")
    @Operation(summary = "根据代码获取告警状态", description = "根据告警状态代码获取详细信息")
    public ApiResponse<AlarmHandlingStatus> getAlarmStateByCode(
            @Parameter(description = "告警状态代码") @PathVariable int code) {
        
        log.info("根据代码获取告警状态: {}", code);
        
        var alarmState = metadataService.getAlarmStateByCode(code);
        
        if (alarmState.isPresent()) {
            return ApiResponse.success("查询成功", alarmState.get());
        } else {
            return ApiResponse.notFound("告警状态");
        }
    }
    
    // ==================== 实体角色相关 ====================
    
    /**
     * 获取所有告警实体角色
     */
    @GetMapping("/entity-roles")
    @Operation(summary = "获取实体角色", description = "获取所有告警实体角色列表")
    public ApiResponse<List<AlarmEntityRole>> getAllAlarmEntityRoles() {
        
        log.info("获取所有告警实体角色");
        
        var entityRoles = metadataService.getAllAlarmEntityRoles();
        
        return ApiResponse.success("查询成功", entityRoles);
    }
    
    /**
     * 根据代码获取告警实体角色
     */
    @GetMapping("/entity-roles/{code}")
    @Operation(summary = "根据代码获取实体角色", description = "根据实体角色代码获取详细信息")
    public ApiResponse<AlarmEntityRole> getAlarmEntityRoleByCode(
            @Parameter(description = "实体角色代码") @PathVariable int code) {
        
        log.info("根据代码获取告警实体角色: {}", code);
        
        var entityRole = metadataService.getAlarmEntityRoleByCode(code);
        
        if (entityRole.isPresent()) {
            return ApiResponse.success("查询成功", entityRole.get());
        } else {
            return ApiResponse.notFound("实体角色");
        }
    }
    
    // ==================== 攻击链相关 ====================
    
    /**
     * 获取攻击链模型
     */
    @GetMapping("/attack-chains")
    @Operation(summary = "获取攻击链模型", description = "获取攻击链模型数据")
    public ApiResponse<List<Map<String, Object>>> getAttackChainModel() {
        
        log.info("获取攻击链模型");
        
        var attackChainModel = metadataService.getAttackChainModel();
        
        return ApiResponse.success("查询成功", attackChainModel);
    }
    
    /**
     * 获取网络杀伤链阶段
     */
    @GetMapping("/cyber-kill-chain")
    @Operation(summary = "获取网络杀伤链", description = "获取所有网络杀伤链阶段")
    public ApiResponse<List<CyberKillChain>> getAllCyberKillChainStages() {
        
        log.info("获取所有网络杀伤链阶段");
        
        var stages = metadataService.getAllCyberKillChainStages();
        
        return ApiResponse.success("查询成功", stages);
    }
    
    /**
     * 获取网络杀伤链阶段详情
     */
    @GetMapping("/cyber-kill-chain/{stage}")
    @Operation(summary = "获取杀伤链阶段详情", description = "根据阶段获取网络杀伤链详细信息")
    public ApiResponse<Map<String, Object>> getCyberKillChainDetails(
            @Parameter(description = "杀伤链阶段") @PathVariable CyberKillChain stage) {
        
        log.info("获取网络杀伤链阶段详情: {}", stage);
        
        var details = metadataService.getCyberKillChainDetails(stage);
        
        return ApiResponse.success("查询成功", details);
    }
    
    // ==================== 缓存管理相关 ====================
    
    /**
     * 获取元数据缓存状态
     */
    @GetMapping("/cache/status")
    @Operation(summary = "获取缓存状态", description = "获取元数据缓存的状态信息")
    public ApiResponse<List<MetadataManager.MetadataStatus>> getMetadataStatus() {
        
        log.info("获取元数据缓存状态");
        
        var status = metadataService.getMetadataStatus();
        
        return ApiResponse.success("查询成功", status);
    }
    
    /**
     * 刷新元数据缓存
     */
    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新元数据缓存", description = "手动刷新所有元数据缓存")
    public ApiResponse<MetadataManager.RefreshAllResult> refreshMetadataCache() {
        
        log.info("刷新元数据缓存");
        
        var result = metadataService.clearAlarmCache();
        
        return ApiResponse.success("缓存刷新完成", result);
    }
}
