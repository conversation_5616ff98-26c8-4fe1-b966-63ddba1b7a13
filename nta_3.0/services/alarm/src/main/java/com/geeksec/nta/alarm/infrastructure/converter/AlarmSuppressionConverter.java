package com.geeksec.nta.alarm.infrastructure.converter;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警抑制转换器
 * 用于在不同层之间转换告警抑制对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class AlarmSuppressionConverter {

    /**
     * 将 AlarmSuppression 实体转换为 AlarmSuppressionResponse
     *
     * @param suppression 告警抑制实体
     * @return 告警抑制响应对象
     */
    public AlarmSuppressionResponse toResponse(AlarmSuppression suppression) {
        if (suppression == null) {
            return null;
        }

        return AlarmSuppressionResponse.builder()
                .suppressionId(suppression.getId())
                .ruleName(suppression.getRuleName())
                .description(suppression.getDescription())
                .suppressionType(suppression.getSuppressionType())
                .enabled(suppression.getEnabled())
                .priority(suppression.getPriority())
                .conditions(convertConditions(suppression.getConditions()))
                .action(suppression.getAction())
                .durationMinutes(suppression.getDurationMinutes())
                .effectiveTimeRanges(convertTimeRanges(suppression.getEffectiveTimeRanges()))
                .attributes(suppression.getAttributes())
                .createdBy(suppression.getCreatedBy())
                .createTime(suppression.getCreateTime())
                .updatedBy(suppression.getUpdatedBy())
                .updateTime(suppression.getUpdateTime())
                .lastTriggeredTime(suppression.getLastTriggeredTime())
                .triggerCount(suppression.getTriggerCount())
                .suppressedAlarmCount(suppression.getSuppressedAlarmCount())
                .build();
    }

    /**
     * 批量转换为响应对象列表
     *
     * @param suppressions 告警抑制实体列表
     * @return 告警抑制响应对象列表
     */
    public List<AlarmSuppressionResponse> toResponseList(List<AlarmSuppression> suppressions) {
        if (suppressions == null) {
            return null;
        }
        return suppressions.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    /**
     * 转换抑制条件
     *
     * @param conditions 原始条件列表
     * @return 响应条件列表
     */
    private List<AlarmSuppressionResponse.SuppressionCondition> convertConditions(
            List<AlarmSuppression.SuppressionCondition> conditions) {
        if (conditions == null) {
            return null;
        }
        return conditions.stream()
                .map(this::convertCondition)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个抑制条件
     *
     * @param condition 原始条件
     * @return 响应条件
     */
    private AlarmSuppressionResponse.SuppressionCondition convertCondition(
            AlarmSuppression.SuppressionCondition condition) {
        if (condition == null) {
            return null;
        }
        return AlarmSuppressionResponse.SuppressionCondition.builder()
                .fieldName(condition.getFieldName())
                .operator(condition.getOperator())
                .values(condition.getValues())
                .enabled(condition.getEnabled())
                .build();
    }

    /**
     * 转换时间范围
     *
     * @param timeRanges 原始时间范围列表
     * @return 响应时间范围列表
     */
    private List<AlarmSuppressionResponse.TimeRange> convertTimeRanges(
            List<AlarmSuppression.TimeRange> timeRanges) {
        if (timeRanges == null) {
            return null;
        }
        return timeRanges.stream()
                .map(this::convertTimeRange)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个时间范围
     *
     * @param timeRange 原始时间范围
     * @return 响应时间范围
     */
    private AlarmSuppressionResponse.TimeRange convertTimeRange(
            AlarmSuppression.TimeRange timeRange) {
        if (timeRange == null) {
            return null;
        }
        return AlarmSuppressionResponse.TimeRange.builder()
                .startTime(timeRange.getStartTime())
                .endTime(timeRange.getEndTime())
                .daysOfWeek(timeRange.getDaysOfWeek())
                .build();
    }
}