package com.geeksec.nta.alarm.interfaces.exception;

import com.geeksec.nta.alarm.domain.exception.AlarmDomainException;
import com.geeksec.common.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理所有异常并返回标准格式的错误响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理领域异常
     */
    @ExceptionHandler(AlarmDomainException.class)
    public ResponseEntity<ApiResponse<Void>> handleDomainException(AlarmDomainException e) {
        log.warn("领域异常: code={}, message={}", e.getErrorCode(), e.getMessage());
        
        HttpStatus status = mapDomainExceptionToHttpStatus(e.getErrorCode());
        ApiResponse<Void> result = ApiResponse.error(e.getErrorCode(), e.getMessage());
        
        return ResponseEntity.status(status).body(result);
    }
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Void>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        
        List<ApiResponse.ErrorDetail> details = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(this::buildErrorDetail)
                .collect(Collectors.toList());
        
        ApiResponse<Void> result = ApiResponse.validationError(details);
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Void>> handleBindException(BindException e) {
        log.warn("绑定异常: {}", e.getMessage());
        
        List<ApiResponse.ErrorDetail> details = e.getFieldErrors()
                .stream()
                .map(this::buildErrorDetail)
                .collect(Collectors.toList());
        
        ApiResponse<Void> result = ApiResponse.validationError(details);
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Void>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束违反异常: {}", e.getMessage());
        
        List<ApiResponse.ErrorDetail> details = e.getConstraintViolations()
                .stream()
                .map(this::buildErrorDetail)
                .collect(Collectors.toList());
        
        ApiResponse<Void> result = ApiResponse.validationError(details);
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Void>> handleMissingParameterException(MissingServletRequestParameterException e) {
        log.warn("缺少请求参数异常: {}", e.getMessage());
        
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        ApiResponse<Void> result = ApiResponse.error("MISSING_PARAMETER", message);
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Void>> handleTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.warn("参数类型不匹配异常: {}", e.getMessage());
        
        String message = String.format("参数类型不匹配: %s，期望类型: %s", 
                e.getName(), e.getRequiredType().getSimpleName());
        ApiResponse<Void> result = ApiResponse.error("TYPE_MISMATCH", message);
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        
        ApiResponse<Void> result = ApiResponse.error("ILLEGAL_ARGUMENT", e.getMessage());
        
        return ResponseEntity.badRequest().body(result);
    }
    
    /**
     * 处理非法状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalStateException(IllegalStateException e) {
        log.warn("非法状态异常: {}", e.getMessage());
        
        ApiResponse<Void> result = ApiResponse.error("ILLEGAL_STATE", e.getMessage());
        
        return ResponseEntity.status(HttpStatus.CONFLICT).body(result);
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Void>> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        
        ApiResponse<Void> result = ApiResponse.systemError();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        log.error("未知异常", e);
        
        ApiResponse<Void> result = ApiResponse.systemError();
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
    
    /**
     * 将领域异常代码映射到HTTP状态码
     */
    private HttpStatus mapDomainExceptionToHttpStatus(String errorCode) {
        return switch (errorCode) {
            case "ALARM_NOT_FOUND", "SUBSCRIPTION_NOT_FOUND", "SUPPRESSION_NOT_FOUND", "METADATA_NOT_FOUND" -> 
                HttpStatus.NOT_FOUND;
            case "INSUFFICIENT_PERMISSION" -> 
                HttpStatus.FORBIDDEN;
            case "DUPLICATE_SUBSCRIPTION_NAME", "DUPLICATE_SUPPRESSION_RULE", "RESOURCE_CONFLICT" -> 
                HttpStatus.CONFLICT;
            case "INVALID_ALARM_STATUS", "INVALID_SUBSCRIPTION_RULE", "INVALID_SUPPRESSION_RULE", "BUSINESS_RULE_VIOLATION" -> 
                HttpStatus.UNPROCESSABLE_ENTITY;
            case "OPERATION_TIMEOUT" -> 
                HttpStatus.REQUEST_TIMEOUT;
            case "EXTERNAL_SERVICE_ERROR" -> 
                HttpStatus.BAD_GATEWAY;
            default -> 
                HttpStatus.BAD_REQUEST;
        };
    }
    
    /**
     * 构建字段错误详情
     */
    private ApiResponse.ErrorDetail buildErrorDetail(FieldError fieldError) {
        return ApiResponse.ErrorDetail.builder()
                .field(fieldError.getField())
                .message(fieldError.getDefaultMessage())
                .rejectedValue(fieldError.getRejectedValue())
                .build();
    }
    
    /**
     * 构建约束违反错误详情
     */
    private ApiResponse.ErrorDetail buildErrorDetail(ConstraintViolation<?> violation) {
        String field = violation.getPropertyPath().toString();
        return ApiResponse.ErrorDetail.builder()
                .field(field)
                .message(violation.getMessage())
                .rejectedValue(violation.getInvalidValue())
                .build();
    }
}
