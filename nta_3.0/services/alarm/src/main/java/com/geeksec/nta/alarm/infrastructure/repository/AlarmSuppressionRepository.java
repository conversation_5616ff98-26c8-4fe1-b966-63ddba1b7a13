package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;

import java.util.List;

/**
 * 告警抑制仓储接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionRepository {

    /**
     * 根据查询条件查找抑制规则
     *
     * @param query 查询条件
     * @return 抑制规则列表
     */
    List<AlarmSuppression> findByQuery(SuppressionListQuery query);

    /**
     * 根据查询条件统计数量
     *
     * @param query 查询条件
     * @return 总数量
     */
    long countByQuery(SuppressionListQuery query);

    /**
     * 根据ID查找抑制规则
     *
     * @param id 抑制规则ID
     * @return 抑制规则
     */
    AlarmSuppression findById(Integer id);

    /**
     * 保存抑制规则
     *
     * @param suppression 抑制规则
     * @return 保存后的抑制规则
     */
    AlarmSuppression save(AlarmSuppression suppression);

    /**
     * 删除抑制规则
     *
     * @param id 抑制规则ID
     */
    void deleteById(Integer id);

    /**
     * 查找所有活跃的抑制规则
     *
     * @return 活跃的抑制规则列表
     */
    List<AlarmSuppression> findActiveSuppressions();
}