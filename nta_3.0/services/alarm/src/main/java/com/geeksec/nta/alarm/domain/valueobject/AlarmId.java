package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 告警ID值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class AlarmId {
    
    private final String value;
    
    private AlarmId(String value) {
        this.value = Objects.requireNonNull(value, "告警ID不能为空");
        if (value.trim().isEmpty()) {
            throw new IllegalArgumentException("告警ID不能为空字符串");
        }
    }
    
    /**
     * 创建告警ID
     * 
     * @param value ID值
     * @return 告警ID
     */
    public static AlarmId of(String value) {
        return new AlarmId(value);
    }
    
    /**
     * 生成新的告警ID
     * 
     * @return 新的告警ID
     */
    public static AlarmId generate() {
        return new AlarmId(java.util.UUID.randomUUID().toString());
    }
}
