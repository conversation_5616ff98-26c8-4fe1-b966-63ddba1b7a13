package com.geeksec.nta.alarm.application.command;

import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除订阅命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteSubscriptionCommand {
    
    /**
     * 订阅ID
     */
    private SubscriptionId subscriptionId;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * 删除原因
     */
    private String reason;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (subscriptionId == null) {
            throw new IllegalArgumentException("订阅ID不能为空");
        }
        if (operator == null || operator.trim().isEmpty()) {
            throw new IllegalArgumentException("操作人不能为空");
        }
    }
}