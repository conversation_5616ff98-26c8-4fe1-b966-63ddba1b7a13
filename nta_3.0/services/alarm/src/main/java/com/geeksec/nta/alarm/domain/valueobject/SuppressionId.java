package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * 抑制规则ID值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public final class SuppressionId {
    
    private final Integer value;
    
    private SuppressionId(Integer value) {
        this.value = Objects.requireNonNull(value, "抑制规则ID不能为空");
        if (value <= 0) {
            throw new IllegalArgumentException("抑制规则ID必须大于0");
        }
    }
    
    /**
     * 创建抑制规则ID
     * 
     * @param value ID值
     * @return 抑制规则ID
     */
    public static SuppressionId of(Integer value) {
        return new SuppressionId(value);
    }
}
