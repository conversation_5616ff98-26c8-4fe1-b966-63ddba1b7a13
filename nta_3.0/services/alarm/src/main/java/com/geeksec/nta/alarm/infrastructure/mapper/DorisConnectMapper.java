package com.geeksec.nta.alarm.infrastructure.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

@Mapper
public interface DorisConnectMapper {

    /**
     * 根据 SessionId 列表查询会话日志。
     *
     * @param sessionIds Session ID 列表
     * @param limit 查询数量
     * @return 返回会话日志的Map列表。
     */
    List<Map<String, Object>> findConnectLogsBySessionIds(@Param("sessionIds") List<String> sessionIds, @Param("limit") int limit);

    /**
     * 根据单个 SessionId 查询会话日志。
     *
     * @param sessionId 单个 Session ID
     * @return 返回单个会话日志的Map，如果找不到则返回null。
     */
    Map<String, Object> findConnectLogBySessionId(@Param("sessionId") String sessionId);
}