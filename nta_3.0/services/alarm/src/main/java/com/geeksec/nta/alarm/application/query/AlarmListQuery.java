package com.geeksec.nta.alarm.application.query;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警列表查询对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
public class AlarmListQuery {
    
    /**
     * 页码（从1开始）
     */
    private Integer page;
    
    /**
     * 页大小
     */
    private Integer size;
    
    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;
    
    /**
     * 告警状态列表
     */
    private List<String> alarmStatuses;
    
    /**
     * 严重程度列表
     */
    private List<String> severities;
    
    /**
     * 攻击者IP列表
     */
    private List<String> attackerIps;
    
    /**
     * 受害者IP列表
     */
    private List<String> victimIps;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection;
    
    /**
     * 是否包含已删除的告警
     */
    private Boolean includeDeleted;
    
    /**
     * 用户ID（用于权限过滤）
     */
    private String userId;
    
    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }
    
    /**
     * 获取限制数量
     */
    public int getLimit() {
        return size;
    }
    
    /**
     * 验证查询参数
     */
    public void validate() {
        if (page == null || page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (size == null || size < 1 || size > 1000) {
            throw new IllegalArgumentException("页大小必须在1-1000之间");
        }
        if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
    }
}
