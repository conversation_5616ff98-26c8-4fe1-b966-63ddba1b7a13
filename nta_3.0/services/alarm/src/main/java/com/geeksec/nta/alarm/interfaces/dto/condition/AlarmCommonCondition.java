package com.geeksec.nta.alarm.interfaces.dto.condition;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警通用查询条件
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AlarmCommonCondition {

    /**
     * 告警ID列表
     */
    private List<Long> alarmIds;

    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;

    /**
     * 威胁等级列表
     */
    private List<String> threatLevels;

    /**
     * 告警状态列表
     */
    private List<String> statuses;

    /**
     * 受害者IP列表
     */
    private List<String> victimIps;

    /**
     * 攻击者IP列表
     */
    private List<String> attackerIps;

    /**
     * 告警标题关键词
     */
    private String titleKeyword;

    /**
     * 告警描述关键词
     */
    private String descriptionKeyword;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建开始时间
     */
    private LocalDateTime createStartTime;

    /**
     * 创建结束时间
     */
    private LocalDateTime createEndTime;

    /**
     * 更新开始时间
     */
    private LocalDateTime updateStartTime;

    /**
     * 更新结束时间
     */
    private LocalDateTime updateEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向
     */
    private String sortDirection = "DESC";

    /**
     * 是否包含已删除的记录
     */
    private Boolean includeDeleted = false;

    /**
     * 用户ID（用于权限过滤）
     */
    private Long userId;

    /**
     * 组织ID（用于权限过滤）
     */
    private Long organizationId;

    /**
     * 检查是否有告警ID过滤条件
     */
    public boolean hasAlarmIdFilter() {
        return alarmIds != null && !alarmIds.isEmpty();
    }

    /**
     * 检查是否有告警类型过滤条件
     */
    public boolean hasAlarmTypeFilter() {
        return alarmTypes != null && !alarmTypes.isEmpty();
    }

    /**
     * 检查是否有威胁等级过滤条件
     */
    public boolean hasThreatLevelFilter() {
        return threatLevels != null && !threatLevels.isEmpty();
    }

    /**
     * 检查是否有状态过滤条件
     */
    public boolean hasStatusFilter() {
        return statuses != null && !statuses.isEmpty();
    }

    /**
     * 检查是否有受害者IP过滤条件
     */
    public boolean hasVictimIpFilter() {
        return victimIps != null && !victimIps.isEmpty();
    }

    /**
     * 检查是否有攻击者IP过滤条件
     */
    public boolean hasAttackerIpFilter() {
        return attackerIps != null && !attackerIps.isEmpty();
    }

    /**
     * 检查是否有时间范围过滤条件
     */
    public boolean hasTimeRangeFilter() {
        return startTime != null || endTime != null;
    }

    /**
     * 检查是否有创建时间范围过滤条件
     */
    public boolean hasCreateTimeRangeFilter() {
        return createStartTime != null || createEndTime != null;
    }

    /**
     * 检查是否有更新时间范围过滤条件
     */
    public boolean hasUpdateTimeRangeFilter() {
        return updateStartTime != null || updateEndTime != null;
    }

    /**
     * 检查是否有关键词过滤条件
     */
    public boolean hasKeywordFilter() {
        return (titleKeyword != null && !titleKeyword.trim().isEmpty()) ||
               (descriptionKeyword != null && !descriptionKeyword.trim().isEmpty());
    }
}