package com.geeksec.nta.alarm.application.service.command;

import com.geeksec.nta.alarm.application.command.CreateSubscriptionCommand;
import com.geeksec.nta.alarm.application.command.DeleteSubscriptionCommand;
import com.geeksec.nta.alarm.application.command.UpdateSubscriptionCommand;
import com.geeksec.nta.alarm.application.command.ToggleSubscriptionCommand;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;

import java.util.List;

/**
 * 告警订阅命令应用服务
 * 负责处理所有订阅状态变更相关的业务逻辑
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSubscriptionCommandService {
    
    /**
     * 创建订阅
     * 
     * @param command 创建命令
     * @return 订阅ID
     */
    SubscriptionId createSubscription(CreateSubscriptionCommand command);
    
    /**
     * 更新订阅
     * 
     * @param command 更新命令
     * @return 是否成功
     */
    boolean updateSubscription(UpdateSubscriptionCommand command);
    
    /**
     * 删除订阅
     * 
     * @param command 删除命令
     * @return 是否成功
     */
    boolean deleteSubscription(DeleteSubscriptionCommand command);
    
    /**
     * 启用/禁用订阅
     * 
     * @param command 切换命令
     * @return 是否成功
     */
    boolean toggleSubscription(ToggleSubscriptionCommand command);
    
    /**
     * 批量删除订阅
     * 
     * @param commands 删除命令列表
     * @return 成功删除的数量
     */
    int batchDeleteSubscriptions(List<DeleteSubscriptionCommand> commands);
    
    /**
     * 批量启用订阅
     * 
     * @param commands 启用命令列表
     * @return 成功启用的数量
     */
    int batchEnableSubscriptions(List<ToggleSubscriptionCommand> commands);
    
    /**
     * 批量禁用订阅
     * 
     * @param commands 禁用命令列表
     * @return 成功禁用的数量
     */
    int batchDisableSubscriptions(List<ToggleSubscriptionCommand> commands);
    
    /**
     * 复制订阅
     * 
     * @param sourceSubscriptionId 源订阅ID
     * @param newSubscriptionName 新订阅名称
     * @param userId 用户ID
     * @return 新订阅ID
     */
    SubscriptionId copySubscription(SubscriptionId sourceSubscriptionId, String newSubscriptionName, String userId);
    
    /**
     * 重置订阅统计信息
     * 
     * @param subscriptionId 订阅ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean resetSubscriptionStatistics(SubscriptionId subscriptionId, String userId);
}
