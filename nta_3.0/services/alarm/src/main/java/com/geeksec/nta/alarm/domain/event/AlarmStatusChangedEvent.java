package com.geeksec.nta.alarm.domain.event;

import com.geeksec.common.enums.AlarmHandlingStatus;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;

/**
 * 告警状态变更事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@RequiredArgsConstructor
public class AlarmStatusChangedEvent {
    
    /**
     * 告警ID
     */
    private final String alarmId;
    
    /**
     * 原状态
     */
    private final AlarmHandlingStatus oldStatus;
    
    /**
     * 新状态
     */
    private final AlarmHandlingStatus newStatus;
    
    /**
     * 操作人
     */
    private final String operator;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt = LocalDateTime.now();
}
