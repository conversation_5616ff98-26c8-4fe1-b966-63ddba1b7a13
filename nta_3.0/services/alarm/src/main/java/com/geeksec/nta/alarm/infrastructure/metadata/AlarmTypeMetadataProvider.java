package com.geeksec.nta.alarm.infrastructure.metadata;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import com.geeksec.nta.alarm.domain.service.MetadataProvider;
import com.geeksec.nta.alarm.domain.entity.AlarmType;
import com.geeksec.nta.alarm.infrastructure.repository.AlarmTypeRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警类型元数据提供者
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlarmTypeMetadataProvider implements MetadataProvider<AlarmType> {
    
    private final AlarmTypeRepository alarmTypeRepository;
    private final Map<String, AlarmType> cache = new ConcurrentHashMap<>();
    private volatile boolean loaded = false;
    private volatile long loadTime = 0;
    
    @Override
    public String getMetadataType() {
        return "ALARM_TYPE";
    }
    
    @Override
    @Cacheable(value = "alarmTypes", key = "'all'")
    public List<AlarmType> getAll() {
        log.debug("获取所有告警类型");
        
        if (!loaded) {
            synchronized (this) {
                if (!loaded) {
                    loadFromRepository();
                }
            }
        }
        
        return List.copyOf(cache.values());
    }
    
    @Override
    @Cacheable(value = "alarmTypes", key = "#code")
    public Optional<AlarmType> getByCode(String code) {
        log.debug("根据代码获取告警类型: {}", code);
        
        if (code == null || code.trim().isEmpty()) {
            return Optional.empty();
        }
        
        if (!loaded) {
            getAll(); // 触发加载
        }
        
        return Optional.ofNullable(cache.get(code));
    }
    
    @Override
    @Cacheable(value = "alarmTypeMapping", key = "'mapping'")
    public Map<String, String> getMapping() {
        log.debug("获取告警类型映射");
        
        return getAll().stream()
                .collect(Collectors.toMap(
                    AlarmType::getTypeCode,
                    AlarmType::getTypeName,
                    (existing, replacement) -> existing
                ));
    }
    
    @Override
    @CacheEvict(value = {"alarmTypes", "alarmTypeMapping"}, allEntries = true)
    public RefreshResult refresh() {
        log.info("刷新告警类型元数据缓存");
        
        try {
            cache.clear();
            loaded = false;
            
            List<AlarmType> types = loadFromRepository();
            
            return new RefreshResult(
                true,
                types.size(),
                "告警类型元数据刷新成功",
                System.currentTimeMillis()
            );
            
        } catch (Exception e) {
            log.error("刷新告警类型元数据失败", e);
            return new RefreshResult(
                false,
                0,
                "刷新失败: " + e.getMessage(),
                System.currentTimeMillis()
            );
        }
    }
    
    @Override
    public boolean isLoaded() {
        return loaded;
    }
    
    @Override
    public long getLoadTime() {
        return loadTime;
    }
    
    /**
     * 从仓储加载数据
     */
    private List<AlarmType> loadFromRepository() {
        log.info("从仓储加载告警类型数据");
        
        try {
            List<AlarmType> types = alarmTypeRepository.findAllEnabled();
            
            cache.clear();
            types.forEach(type -> cache.put(type.getTypeCode(), type));
            
            loaded = true;
            loadTime = System.currentTimeMillis();
            
            log.info("成功加载{}个告警类型", types.size());
            return types;
            
        } catch (Exception e) {
            log.error("加载告警类型数据失败", e);
            throw new RuntimeException("加载告警类型数据失败", e);
        }
    }
}
