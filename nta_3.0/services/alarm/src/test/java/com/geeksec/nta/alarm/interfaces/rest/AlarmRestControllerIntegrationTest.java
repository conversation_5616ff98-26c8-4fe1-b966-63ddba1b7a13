package com.geeksec.nta.alarm.interfaces.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 告警REST控制器集成测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("告警REST控制器集成测试")
class AlarmRestControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    @DisplayName("完整的告警管理流程测试")
    void completeAlarmManagementFlow() throws Exception {
        // 1. 查询告警列表（初始为空）
        mockMvc.perform(get("/api/v1/alarms")
                        .param("page", "1")
                        .param("size", "20")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(0));
        
        // 2. 查询不存在的告警详情
        mockMvc.perform(get("/api/v1/alarms/non-existent-id")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false));
        
        // 3. 测试参数验证
        mockMvc.perform(get("/api/v1/alarms")
                        .param("page", "0") // 无效页码
                        .param("size", "2000") // 超出限制
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.error.code").value("VALIDATION_ERROR"));
        
        // 4. 测试告警统计接口
        mockMvc.perform(get("/api/v1/alarms/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 5. 测试告警趋势接口
        mockMvc.perform(get("/api/v1/alarms/trends")
                        .param("startTime", "2024-01-01T00:00:00")
                        .param("endTime", "2024-01-02T00:00:00")
                        .param("granularity", "hour")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    
    @Test
    @DisplayName("告警订阅管理流程测试")
    void alarmSubscriptionManagementFlow() throws Exception {
        // 1. 查询订阅列表（初始为空）
        mockMvc.perform(get("/api/v1/subscriptions")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 2. 创建订阅
        String createSubscriptionRequest = """
                {
                    "subscriptionName": "测试订阅",
                    "description": "这是一个测试订阅",
                    "enabled": true,
                    "priorityLevel": 1,
                    "matchRules": [
                        {
                            "fieldName": "alarmType",
                            "operator": "equals",
                            "value": "SCAN"
                        }
                    ],
                    "notificationChannels": [
                        {
                            "channelType": "EMAIL",
                            "channelConfig": {
                                "recipients": ["<EMAIL>"]
                            }
                        }
                    ]
                }
                """;
        
        mockMvc.perform(post("/api/v1/subscriptions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(createSubscriptionRequest))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true));
        
        // 3. 测试订阅规则
        String testSubscriptionRequest = """
                {
                    "alarmType": "SCAN",
                    "severity": "HIGH",
                    "attackerIp": "********",
                    "victimIp": "*************"
                }
                """;
        
        mockMvc.perform(post("/api/v1/subscriptions/test")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(testSubscriptionRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    
    @Test
    @DisplayName("告警抑制管理流程测试")
    void alarmSuppressionManagementFlow() throws Exception {
        // 1. 查询抑制规则列表（初始为空）
        mockMvc.perform(get("/api/v1/suppressions")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 2. 创建抑制规则
        String createSuppressionRequest = """
                {
                    "victim": "*************",
                    "attacker": "********",
                    "label": "SCAN",
                    "description": "测试扫描抑制规则",
                    "operator": "test-user"
                }
                """;
        
        mockMvc.perform(post("/api/v1/suppressions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(createSuppressionRequest))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true));
        
        // 3. 检查抑制规则
        String checkSuppressionRequest = """
                {
                    "victim": "*************",
                    "attacker": "********",
                    "label": "SCAN"
                }
                """;
        
        mockMvc.perform(post("/api/v1/suppressions/check")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(checkSuppressionRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 4. 获取抑制统计
        mockMvc.perform(get("/api/v1/suppressions/statistics")
                        .param("days", "30")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    
    @Test
    @DisplayName("告警元数据查询流程测试")
    void alarmMetadataQueryFlow() throws Exception {
        // 1. 获取告警类型
        mockMvc.perform(get("/api/v1/metadata/alarm-types")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 2. 获取告警目标
        mockMvc.perform(get("/api/v1/metadata/alarm-targets")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 3. 获取告警状态
        mockMvc.perform(get("/api/v1/metadata/alarm-statuses")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 4. 获取攻击链模型
        mockMvc.perform(get("/api/v1/metadata/attack-chains")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 5. 获取缓存状态
        mockMvc.perform(get("/api/v1/metadata/cache/status")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        // 6. 刷新缓存
        mockMvc.perform(post("/api/v1/metadata/cache/refresh")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }
    
    @Test
    @DisplayName("错误处理测试")
    void errorHandlingTest() throws Exception {
        // 1. 测试404错误
        mockMvc.perform(get("/api/v1/non-existent-endpoint")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
        
        // 2. 测试方法不支持错误
        mockMvc.perform(patch("/api/v1/alarms")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isMethodNotAllowed());
        
        // 3. 测试内容类型不支持错误
        mockMvc.perform(post("/api/v1/alarms")
                        .contentType(MediaType.TEXT_PLAIN)
                        .content("invalid content"))
                .andExpect(status().isUnsupportedMediaType());
    }
}
