package com.geeksec.nta.alarm.service;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.subscription.*;
import com.geeksec.nta.alarm.entity.AlarmSubscription;
import com.geeksec.nta.alarm.mapper.AlarmSubscriptionMapper;
import com.geeksec.nta.alarm.mapper.NotificationLogMapper;
import com.geeksec.nta.alarm.service.impl.AlarmSubscriptionServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 告警订阅服务测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class AlarmSubscriptionServiceTest {
    
    @Mock
    private AlarmSubscriptionMapper subscriptionMapper;
    
    @Mock
    private NotificationLogMapper notificationLogMapper;
    
    @InjectMocks
    private AlarmSubscriptionServiceImpl subscriptionService;
    
    private String testUserId;
    private CreateSubscriptionRequest createRequest;
    private AlarmSubscription testSubscription;
    
    @BeforeEach
    void setUp() {
        testUserId = "test-user-001";
        
        // 创建测试订阅请求
        createRequest = new CreateSubscriptionRequest();
        createRequest.setSubscriptionName("测试订阅");
        createRequest.setDescription("这是一个测试订阅");
        createRequest.setPriorityLevel(1);
        createRequest.setFrequencyType(AlarmSubscription.FrequencyType.REAL_TIME);
        
        // 创建匹配规则
        List<SubscriptionRuleDto> rules = Arrays.asList(
                SubscriptionRuleDto.createEqualsRule("alarmType", "恶意软件"),
                SubscriptionRuleDto.createContainsRule("srcIp", "192.168.1")
        );
        createRequest.setMatchRules(rules);
        
        // 创建通知渠道
        List<NotificationChannelDto> channels = Arrays.asList(
                NotificationChannelDto.createEmailChannel("<EMAIL>", "default_email_template"),
                NotificationChannelDto.createKafkaChannel("alarm-notifications", "default_kafka_template")
        );
        createRequest.setNotificationChannels(channels);
        
        // 创建测试订阅实体
        testSubscription = new AlarmSubscription();
        testSubscription.setId("test-subscription-001");
        testSubscription.setUserId(testUserId);
        testSubscription.setSubscriptionName("测试订阅");
        testSubscription.setDescription("这是一个测试订阅");
        testSubscription.setEnabled(true);
        testSubscription.setPriorityLevel(1);
        testSubscription.setMatchRules(rules);
        testSubscription.setNotificationChannels(channels);
        testSubscription.setFrequencyType(AlarmSubscription.FrequencyType.REAL_TIME);
        testSubscription.setTriggerCount(0);
        testSubscription.setCreatedTime(LocalDateTime.now());
        testSubscription.setUpdatedTime(LocalDateTime.now());
    }
    
    @Test
    void testCreateSubscription() {
        // Given
        when(subscriptionMapper.countByUserIdAndName(eq(testUserId), eq("测试订阅"), isNull()))
                .thenReturn(0L);
        when(subscriptionMapper.insert(any(AlarmSubscription.class))).thenReturn(1);
        
        // When
        String subscriptionId = subscriptionService.createSubscription(createRequest, testUserId);
        
        // Then
        assertNotNull(subscriptionId);
        verify(subscriptionMapper).countByUserIdAndName(eq(testUserId), eq("测试订阅"), isNull());
        verify(subscriptionMapper).insert(any(AlarmSubscription.class));
    }
    
    @Test
    void testCreateSubscriptionWithDuplicateName() {
        // Given
        when(subscriptionMapper.countByUserIdAndName(eq(testUserId), eq("测试订阅"), isNull()))
                .thenReturn(1L);
        
        // When & Then
        assertThrows(Exception.class, () -> {
            subscriptionService.createSubscription(createRequest, testUserId);
        });
        
        verify(subscriptionMapper).countByUserIdAndName(eq(testUserId), eq("测试订阅"), isNull());
        verify(subscriptionMapper, never()).insert(any(AlarmSubscription.class));
    }
    
    @Test
    void testGetSubscription() {
        // Given
        when(subscriptionMapper.selectOneById("test-subscription-001")).thenReturn(testSubscription);
        
        // When
        AlarmSubscriptionVo result = subscriptionService.getSubscription("test-subscription-001", testUserId);
        
        // Then
        assertNotNull(result);
        assertEquals("test-subscription-001", result.getId());
        assertEquals("测试订阅", result.getSubscriptionName());
        assertEquals(testUserId, result.getUserId());
        
        verify(subscriptionMapper).selectOneById("test-subscription-001");
    }
    
    @Test
    void testDeleteSubscription() {
        // Given
        when(subscriptionMapper.selectOneById("test-subscription-001")).thenReturn(testSubscription);
        when(subscriptionMapper.deleteById("test-subscription-001")).thenReturn(1);
        
        // When
        boolean result = subscriptionService.deleteSubscription("test-subscription-001", testUserId);
        
        // Then
        assertTrue(result);
        verify(subscriptionMapper).selectOneById("test-subscription-001");
        verify(subscriptionMapper).deleteById("test-subscription-001");
    }
    
    @Test
    void testToggleSubscription() {
        // Given
        when(subscriptionMapper.selectOneById("test-subscription-001")).thenReturn(testSubscription);
        when(subscriptionMapper.update(any(AlarmSubscription.class))).thenReturn(1);
        
        // When
        boolean result = subscriptionService.toggleSubscription("test-subscription-001", false, testUserId);
        
        // Then
        assertTrue(result);
        verify(subscriptionMapper).selectOneById("test-subscription-001");
        verify(subscriptionMapper).update(any(AlarmSubscription.class));
    }
    
    @Test
    void testTestSubscription() {
        // Given
        TestSubscriptionRequest testRequest = new TestSubscriptionRequest();
        testRequest.setMatchRules(Arrays.asList(
                SubscriptionRuleDto.createEqualsRule("alarmType", "恶意软件"),
                SubscriptionRuleDto.createContainsRule("srcIp", "*************")
        ));
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("alarmType", "恶意软件");
        testData.put("srcIp", "*************");
        testData.put("dstIp", "********");
        testRequest.setTestData(testData);
        
        // When
        TestSubscriptionResult result = subscriptionService.testSubscription(testRequest);
        
        // Then
        assertNotNull(result);
        assertTrue(result.getMatched());
        assertEquals(2, result.getMatchedRulesCount());
        assertEquals(2, result.getTotalRulesCount());
        assertNotNull(result.getRuleDetails());
        assertEquals(2, result.getRuleDetails().size());
        
        // 验证规则匹配详情
        TestSubscriptionResult.RuleMatchDetail firstRule = result.getRuleDetails().get(0);
        assertTrue(firstRule.getMatched());
        assertEquals("alarmType", firstRule.getFieldName());
        assertEquals("恶意软件", firstRule.getExpectedValue());
        assertEquals("恶意软件", firstRule.getActualValue());
        
        TestSubscriptionResult.RuleMatchDetail secondRule = result.getRuleDetails().get(1);
        assertTrue(secondRule.getMatched());
        assertEquals("srcIp", secondRule.getFieldName());
        assertEquals("192.168.1", secondRule.getExpectedValue());
        assertEquals("*************", secondRule.getActualValue());
    }
    
    @Test
    void testTestSubscriptionWithMismatch() {
        // Given
        TestSubscriptionRequest testRequest = new TestSubscriptionRequest();
        testRequest.setMatchRules(Arrays.asList(
                SubscriptionRuleDto.createEqualsRule("alarmType", "恶意软件"),
                SubscriptionRuleDto.createContainsRule("srcIp", "10.0.0")
        ));
        
        Map<String, Object> testData = new HashMap<>();
        testData.put("alarmType", "恶意软件");
        testData.put("srcIp", "*************");
        testRequest.setTestData(testData);
        
        // When
        TestSubscriptionResult result = subscriptionService.testSubscription(testRequest);
        
        // Then
        assertNotNull(result);
        assertFalse(result.getMatched());
        assertEquals(1, result.getMatchedRulesCount());
        assertEquals(2, result.getTotalRulesCount());
        
        // 验证第一个规则匹配，第二个规则不匹配
        TestSubscriptionResult.RuleMatchDetail firstRule = result.getRuleDetails().get(0);
        assertTrue(firstRule.getMatched());
        
        TestSubscriptionResult.RuleMatchDetail secondRule = result.getRuleDetails().get(1);
        assertFalse(secondRule.getMatched());
    }
    

}
