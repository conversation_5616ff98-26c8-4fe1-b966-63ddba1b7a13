package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.dto.condition.AlarmRoleJudgeCondition;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警关系分析门面服务测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
class AlarmRelationAnalysisFacadeTest {
    
    // 注意：这是一个基础的测试框架，实际测试需要根据具体的Spring配置和数据库配置来调整
    
    @Test
    void testCreateAlarmRelationGraphWithEmptyMap() {
        // 测试空告警数据的处理
        Map<String, Object> emptyAlarmMap = new HashMap<>();
        
        // 这里需要注入实际的服务实例进行测试
        // AlarmRelationAnalysisFacade facade = ...;
        // Map<String, Object> result = facade.createAlarmRelationGraph(emptyAlarmMap);
        
        // 验证返回的图谱数据结构
        // assertNotNull(result);
        // assertTrue(result.containsKey("vertex"));
        // assertTrue(result.containsKey("edge"));
        // assertTrue(result.containsKey("label_vertex"));
    }
    
    @Test
    void testCreateAlarmRelationGraphWithValidData() {
        // 测试有效告警数据的处理
        Map<String, Object> alarmMap = new HashMap<>();
        alarmMap.put("alarm_knowledge_id", 100002); // 扫描行为
        alarmMap.put("alarm_type", "scan");
        alarmMap.put("alarm_session_list", List.of("session1", "session2"));
        
        // 这里需要注入实际的服务实例进行测试
        // AlarmRelationAnalysisFacade facade = ...;
        // Map<String, Object> result = facade.createAlarmRelationGraph(alarmMap);
        
        // 验证返回的图谱数据
        // assertNotNull(result);
        // assertNotNull(result.get("vertex"));
        // assertNotNull(result.get("edge"));
        // assertNotNull(result.get("label_vertex"));
    }
    
    @Test
    void testCreateAlarmRelationGraphByRole() {
        // 测试基于角色的告警关系分析
        AlarmRoleJudgeCondition condition = new AlarmRoleJudgeCondition();
        condition.setIpAddr("*************");
        condition.setRole(List.of("attacker", "victim"));
        condition.setQueryNum(10);
        
        AlarmRoleJudgeCondition.TimeRange timeRange = new AlarmRoleJudgeCondition.TimeRange();
        timeRange.setLeft(System.currentTimeMillis() - 24 * 60 * 60 * 1000L); // 24小时前
        timeRange.setRight(System.currentTimeMillis());
        condition.setTimeRange(timeRange);
        
        // 这里需要注入实际的服务实例进行测试
        // AlarmRelationAnalysisFacade facade = ...;
        // Map<String, Object> result = facade.createAlarmRelationGraphByRole(condition);
        
        // 验证返回的图谱数据
        // assertNotNull(result);
        // assertTrue(result.containsKey("vertex"));
        // assertTrue(result.containsKey("edge"));
        // assertTrue(result.containsKey("label_vertex"));
    }
}
