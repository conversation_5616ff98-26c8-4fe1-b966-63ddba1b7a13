package com.geeksec.nta.alarm.application.service.impl;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.AlarmListQuery;
import com.geeksec.nta.alarm.domain.aggregate.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.infrastructure.converter.AlarmConverter;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 告警查询服务测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("告警查询服务测试")
class AlarmQueryServiceImplTest {
    
    @Mock
    private AlarmRepository alarmRepository;
    
    @Mock
    private AlarmConverter alarmConverter;
    
    @InjectMocks
    private AlarmQueryServiceImpl alarmQueryService;
    
    private AlarmListQuery testQuery;
    private Alarm testAlarm;
    private AlarmId testAlarmId;
    
    @BeforeEach
    void setUp() {
        testAlarmId = new AlarmId("test-alarm-001");
        
        testQuery = AlarmListQuery.builder()
                .page(1)
                .size(20)
                .build();
        
        testAlarm = createTestAlarm();
    }
    
    @Test
    @DisplayName("查询告警列表 - 成功")
    void queryAlarmList_Success() {
        // Given
        List<Alarm> alarms = List.of(testAlarm);
        List<AlarmListResponse> responses = List.of(createTestAlarmListResponse());
        
        when(alarmRepository.countByQuery(testQuery)).thenReturn(1L);
        when(alarmRepository.findByQuery(testQuery)).thenReturn(alarms);
        when(alarmConverter.toListResponses(alarms)).thenReturn(responses);
        
        // When
        PageResultVo<AlarmListResponse> result = alarmQueryService.queryAlarmList(testQuery);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getRecords()).hasSize(1);
        assertThat(result.getCurrent()).isEqualTo(1);
        assertThat(result.getSize()).isEqualTo(20);
        
        verify(alarmRepository).countByQuery(testQuery);
        verify(alarmRepository).findByQuery(testQuery);
        verify(alarmConverter).toListResponses(alarms);
    }
    
    @Test
    @DisplayName("查询告警列表 - 空结果")
    void queryAlarmList_EmptyResult() {
        // Given
        when(alarmRepository.countByQuery(testQuery)).thenReturn(0L);
        
        // When
        PageResultVo<AlarmListResponse> result = alarmQueryService.queryAlarmList(testQuery);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getTotal()).isEqualTo(0L);
        assertThat(result.getRecords()).isEmpty();
        
        verify(alarmRepository).countByQuery(testQuery);
        verify(alarmRepository, never()).findByQuery(any());
    }
    
    @Test
    @DisplayName("查询告警列表 - 参数验证失败")
    void queryAlarmList_ValidationFailed() {
        // Given
        AlarmListQuery invalidQuery = AlarmListQuery.builder()
                .page(0) // 无效页码
                .size(20)
                .build();
        
        // When & Then
        assertThatThrownBy(() -> alarmQueryService.queryAlarmList(invalidQuery))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("页码必须大于0");
    }
    
    @Test
    @DisplayName("根据ID查询告警详情 - 成功")
    void queryAlarmById_Success() {
        // Given
        AlarmDetailResponse expectedResponse = createTestAlarmDetailResponse();
        
        when(alarmRepository.findById(testAlarmId)).thenReturn(Optional.of(testAlarm));
        when(alarmConverter.toDetailResponse(testAlarm)).thenReturn(expectedResponse);
        
        // When
        AlarmDetailResponse result = alarmQueryService.queryAlarmById(testAlarmId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testAlarmId.value());
        
        verify(alarmRepository).findById(testAlarmId);
        verify(alarmConverter).toDetailResponse(testAlarm);
    }
    
    @Test
    @DisplayName("根据ID查询告警详情 - 告警不存在")
    void queryAlarmById_AlarmNotFound() {
        // Given
        when(alarmRepository.findById(testAlarmId)).thenReturn(Optional.empty());
        
        // When & Then
        assertThatThrownBy(() -> alarmQueryService.queryAlarmById(testAlarmId))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("告警不存在");
        
        verify(alarmRepository).findById(testAlarmId);
        verify(alarmConverter, never()).toDetailResponse(any());
    }
    
    @Test
    @DisplayName("检查告警是否存在 - 存在")
    void existsById_AlarmExists() {
        // Given
        when(alarmRepository.existsById(testAlarmId)).thenReturn(true);
        
        // When
        boolean result = alarmQueryService.existsById(testAlarmId);
        
        // Then
        assertThat(result).isTrue();
        
        verify(alarmRepository).existsById(testAlarmId);
    }
    
    @Test
    @DisplayName("检查告警是否存在 - 不存在")
    void existsById_AlarmNotExists() {
        // Given
        when(alarmRepository.existsById(testAlarmId)).thenReturn(false);
        
        // When
        boolean result = alarmQueryService.existsById(testAlarmId);
        
        // Then
        assertThat(result).isFalse();
        
        verify(alarmRepository).existsById(testAlarmId);
    }
    
    @Test
    @DisplayName("统计告警总数 - 成功")
    void countAlarms_Success() {
        // Given
        long expectedCount = 100L;
        when(alarmRepository.countByQuery(testQuery)).thenReturn(expectedCount);
        
        // When
        long result = alarmQueryService.countAlarms(testQuery);
        
        // Then
        assertThat(result).isEqualTo(expectedCount);
        
        verify(alarmRepository).countByQuery(testQuery);
    }
    
    @Test
    @DisplayName("查询告警列表 - 仓储异常")
    void queryAlarmList_RepositoryException() {
        // Given
        when(alarmRepository.countByQuery(testQuery))
                .thenThrow(new RuntimeException("数据库连接失败"));
        
        // When & Then
        assertThatThrownBy(() -> alarmQueryService.queryAlarmList(testQuery))
                .isInstanceOf(RuntimeException.class)
                .hasMessageContaining("获取告警列表失败");
        
        verify(alarmRepository).countByQuery(testQuery);
    }
    
    // 辅助方法
    private Alarm createTestAlarm() {
        // 创建测试告警对象
        // 这里需要根据实际的Alarm类实现来创建
        return mock(Alarm.class);
    }
    
    private AlarmListResponse createTestAlarmListResponse() {
        return AlarmListResponse.builder()
                .id(testAlarmId.value())
                .alarmType("TEST_ALARM")
                .severity("HIGH")
                .status("PENDING")
                .build();
    }
    
    private AlarmDetailResponse createTestAlarmDetailResponse() {
        return AlarmDetailResponse.builder()
                .id(testAlarmId.value())
                .alarmType("TEST_ALARM")
                .severity("HIGH")
                .status("PENDING")
                .description("测试告警详情")
                .build();
    }
}
