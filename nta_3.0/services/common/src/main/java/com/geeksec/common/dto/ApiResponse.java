package com.geeksec.common.dto;

import java.util.List;

import org.springframework.hateoas.Link;
import org.springframework.hateoas.Links;
import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用API响应对象
 *
 * 统一的API响应格式，支持：
 * - 基础响应（成功/失败）
 * - 分页响应
 * - HATEOAS链接
 * - 时间戳
 *
 * <AUTHOR>
 * @param <T> 响应数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "通用API响应")
public class ApiResponse<T> {
    /**
     * 是否成功
     */
    @Schema(description = "是否成功", example = "true")
    private boolean success;

    /**
     * 响应码
     */
    @Schema(description = "响应码", example = "200")
    private int code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息", example = "操作成功")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "1640995200000")
    private Long timestamp;

    /**
     * HATEOAS链接
     */
    @Schema(description = "HATEOAS链接")
    private Links links;

    /**
     * 分页信息
     */
    @Schema(description = "分页信息")
    private PageInfo pageInfo;

    /**
     * 构造函数 - 带参数并自动设置时间戳
     */
    public ApiResponse(boolean success, int code, String message, T data) {
        this();
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Lombok @Data 注解会自动生成 getter/setter 方法，这里移除手动定义的方法

    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, HttpStatus.OK.value(), "操作成功", data);
    }

    /**
     * 创建成功响应（自定义消息）
     *
     * @param message 成功消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, HttpStatus.OK.value(), message, data);
    }

    /**
     * 创建带链接的成功响应
     *
     * @param data  响应数据
     * @param links 链接列表
     * @param <T>   数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data, List<Link> links) {
        ApiResponse<T> response = success(data);
        response.setLinks(Links.of(links));
        return response;
    }

    /**
     * 创建带分页信息的成功响应
     *
     * @param data     响应数据
     * @param pageInfo 分页信息
     * @param <T>      数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data, PageInfo pageInfo) {
        ApiResponse<T> response = success(data);
        response.setPageInfo(pageInfo);
        return response;
    }

    /**
     * 创建带分页信息和链接的成功响应
     *
     * @param data     响应数据
     * @param pageInfo 分页信息
     * @param links    链接列表
     * @param <T>      数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data, PageInfo pageInfo, List<Link> links) {
        ApiResponse<T> response = success(data);
        response.setPageInfo(pageInfo);
        response.setLinks(Links.of(links));
        return response;
    }

    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return error(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
    }

    /**
     * 创建错误响应
     *
     * @param code    错误代码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(false, code, message, null);
    }

    /**
     * 创建参数验证失败响应
     *
     * @param message 错误信息
     * @param <T>     数据类型
     * @return 参数验证失败响应
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return error(HttpStatus.BAD_REQUEST.value(), message);
    }

    /**
     * 创建未授权响应
     *
     * @param message 错误信息
     * @param <T>     数据类型
     * @return 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized(String message) {
        return error(HttpStatus.UNAUTHORIZED.value(), message);
    }

    /**
     * 创建禁止访问响应
     *
     * @param message 错误信息
     * @param <T>     数据类型
     * @return 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return error(HttpStatus.FORBIDDEN.value(), message);
    }

    /**
     * 创建错误响应（带数据）
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    错误数据
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(int code, String message, T data) {
        ApiResponse<T> response = error(code, message);
        response.setData(data);
        return response;
    }

    /**
     * 分页信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分页信息")
    public static class PageInfo {

        @Schema(description = "当前页码", example = "1")
        private int page;

        @Schema(description = "每页大小", example = "20")
        private int size;

        @Schema(description = "总记录数", example = "100")
        private long totalElements;

        @Schema(description = "总页数", example = "5")
        private int totalPages;

        @Schema(description = "是否有下一页", example = "true")
        private boolean hasNext;

        @Schema(description = "是否有上一页", example = "false")
        private boolean hasPrevious;

        /**
         * 构造函数 - 自动计算分页信息
         */
        public PageInfo(int page, int size, long totalElements) {
            this.page = page;
            this.size = size;
            this.totalElements = totalElements;
            this.totalPages = (int) Math.ceil((double) totalElements / size);
            this.hasNext = page < totalPages;
            this.hasPrevious = page > 1;
        }
    }

    /**
     * 错误详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "错误详情")
    public static class ErrorDetail {
        
        @Schema(description = "字段名", example = "username")
        private String field;
        
        @Schema(description = "错误消息", example = "用户名不能为空")
        private String message;
        
        @Schema(description = "被拒绝的值", example = "null")
        private Object rejectedValue;
    }

}
