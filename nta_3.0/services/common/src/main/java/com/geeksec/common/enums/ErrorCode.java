package com.geeksec.common.enums;

/**
 * 错误代码枚举
 * 定义系统中常用的错误代码和错误信息
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public enum ErrorCode {
    
    // 通用错误代码
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    
    // 业务错误代码
    BUSINESS_ERROR(1000, "业务处理失败"),
    DATA_NOT_FOUND(1001, "数据不存在"),
    DATA_ALREADY_EXISTS(1002, "数据已存在"),
    OPERATION_FAILED(1003, "操作失败"),
    INVALID_OPERATION(1004, "无效操作"),
    
    // 认证相关错误
    LOGIN_FAILED(2001, "登录失败"),
    TOKEN_EXPIRED(2002, "令牌已过期"),
    TOKEN_INVALID(2003, "令牌无效"),
    PERMISSION_DENIED(2004, "权限不足"),
    
    // 数据库相关错误
    DATABASE_ERROR(3001, "数据库操作失败"),
    DATA_INTEGRITY_ERROR(3002, "数据完整性错误"),
    TRANSACTION_FAILED(3003, "事务处理失败"),
    
    // 文件相关错误
    FILE_NOT_FOUND(4001, "文件不存在"),
    FILE_UPLOAD_FAILED(4002, "文件上传失败"),
    FILE_SIZE_EXCEEDED(4003, "文件大小超出限制"),
    FILE_TYPE_NOT_SUPPORTED(4004, "不支持的文件类型"),
    
    // 网络相关错误
    NETWORK_ERROR(5001, "网络连接失败"),
    TIMEOUT_ERROR(5002, "请求超时"),
    SERVICE_UNAVAILABLE(5003, "服务不可用");
    
    private final int code;
    private final String message;
    
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据错误代码获取ErrorCode枚举
     * 
     * @param code 错误代码
     * @return ErrorCode枚举，如果未找到则返回SYSTEM_ERROR
     */
    public static ErrorCode fromCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return SYSTEM_ERROR;
    }
    
    @Override
    public String toString() {
        return String.format("ErrorCode{code=%d, message='%s'}", code, message);
    }
}