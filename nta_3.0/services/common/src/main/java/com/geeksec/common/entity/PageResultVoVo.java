package com.geeksec.common.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果封装类
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResultVoVo<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 总页数
     */
    public Long getPages() {
        if (size == null || size <= 0) {
            return 0L;
        }
        return (total + size - 1) / size;
    }
    
    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return current != null && current > 1;
    }
    
    /**
     * 是否有下一页
     */
    public boolean hasNext() {
        return current != null && current < getPages();
    }
    
    /**
     * 是否为第一页
     */
    public boolean isFirst() {
        return current != null && current.equals(1L);
    }
    
    /**
     * 是否为最后一页
     */
    public boolean isLast() {
        return current != null && current.equals(getPages());
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResultVoVo<T> empty() {
        return PageResultVoVo.<T>builder()
                .records(List.of())
                .total(0L)
                .current(1L)
                .size(10L)
                .build();
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResultVoVo<T> empty(Long current, Long size) {
        return PageResultVoVo.<T>builder()
                .records(List.of())
                .total(0L)
                .current(current)
                .size(size)
                .build();
    }
}