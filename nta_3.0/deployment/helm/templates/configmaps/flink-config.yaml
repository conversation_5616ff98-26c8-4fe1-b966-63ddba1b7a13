{{- define "nta.flinkConfigMap" -}}
{{- $jobName := .jobName -}}
{{- $jobConfig := .jobConfig -}}
{{- $namespace := .namespace -}}
{{- $root := .root -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-{{ $jobName }}-config
  namespace: {{ $namespace }}
  labels:
    {{- include "nta.labels" $root | nindent 4 }}
    app: flink-{{ $jobName }}
data:
  config.properties: |
    # Kafka配置
    kafka.bootstrap.servers={{ $root.Values.infrastructure.kafka.host }}:{{ $root.Values.infrastructure.kafka.port }}
    {{- if $jobConfig.kafka.groupId }}
    kafka.group.id={{ $jobConfig.kafka.groupId }}
    {{- end }}

    {{- if $jobConfig.kafka.topicMeta }}
    kafka.topic.meta={{ $jobConfig.kafka.topicMeta }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonConnect }}
    kafka.topic.connect={{ $jobConfig.kafka.topicJsonConnect }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonHttp }}
    kafka.topic.http={{ $jobConfig.kafka.topicJsonHttp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonDns }}
    kafka.topic.dns={{ $jobConfig.kafka.topicJsonDns }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonSsl }}
    kafka.topic.ssl={{ $jobConfig.kafka.topicJsonSsl }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonSsh }}
    kafka.topic.ssh={{ $jobConfig.kafka.topicJsonSsh }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonRlogin }}
    kafka.topic.rlogin={{ $jobConfig.kafka.topicJsonRlogin }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonTelnet }}
    kafka.topic.telnet={{ $jobConfig.kafka.topicJsonTelnet }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonRdp }}
    kafka.topic.rdp={{ $jobConfig.kafka.topicJsonRdp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonVnc }}
    kafka.topic.vnc={{ $jobConfig.kafka.topicJsonVnc }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonXdmcp }}
    kafka.topic.xdmcp={{ $jobConfig.kafka.topicJsonXdmcp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonNtp }}
    kafka.topic.ntp={{ $jobConfig.kafka.topicJsonNtp }}
    {{- end }}
    {{- if $jobConfig.kafka.topicJsonIcmp }}
    kafka.topic.icmp={{ $jobConfig.kafka.topicJsonIcmp }}
    {{- end }}
    {{- if $jobConfig.kafka.patternToggleTopic }}
    kafka.patternToggle.topic={{ $jobConfig.kafka.patternToggleTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.patternToggleGroupId }}
    kafka.patternToggle.group.id={{ $jobConfig.kafka.patternToggleGroupId }}
    {{- end }}
    {{- if $jobConfig.kafka.outputTopic }}
    kafka.output.topic={{ $jobConfig.kafka.outputTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.certTopic }}
    cert.kafka.topic.name={{ $jobConfig.kafka.certTopic }}
    {{- end }}
    {{- if $jobConfig.kafka.certGroupId }}
    cert.kafka.group.id={{ $jobConfig.kafka.certGroupId }}
    {{- end }}
    kafka.security.protocol={{ $root.Values.infrastructure.kafka.security.protocol }}
    kafka.sasl.mechanism={{ $root.Values.infrastructure.kafka.security.mechanism }}

    # Elasticsearch配置
    elasticsearch.host={{ $root.Values.infrastructure.elasticsearch.host }}
    elasticsearch.port={{ $root.Values.infrastructure.elasticsearch.port }}

    # Doris配置
    doris.host={{ $root.Values.infrastructure.doris.cluster.name }}-fe.{{ $root.Values.global.namespace }}.svc:{{ $root.Values.infrastructure.doris.fe.queryPort | default "9030" }}
    doris.database=nta
    doris.sink.parallelism={{ $jobConfig.parallelism.dorisSink | default "4" }}
    doris.jdbc.url=jdbc:mysql://{{ $root.Values.infrastructure.doris.cluster.name }}-fe.{{ $root.Values.global.namespace }}.svc:{{ $root.Values.infrastructure.doris.fe.queryPort | default "9030" }}/{{ $root.Values.infrastructure.doris.database | default "nta" }}?useSSL=false&serverTimezone=Asia/Shanghai
    doris.jdbc.username={{ $root.Values.infrastructure.doris.username | default "" }}
    doris.jdbc.password={{ $root.Values.infrastructure.doris.password | default "" }}
    doris.jdbc.driver=com.mysql.cj.jdbc.Driver

    # MinIO配置 - 用于存储和读取数据
    {{- if $jobConfig.minio }}
    {{- if $jobConfig.minio.enabled }}
    minio.endpoint={{ $jobConfig.minio.endpoint }}
    # 凭据通过环境变量传入，这里不需要配置
    minio.bucket.name={{ $jobConfig.minio.bucket }}
    {{- if $jobConfig.minio.pathPrefix }}
    minio.path.prefix={{ $jobConfig.minio.pathPrefix }}
    {{- end }}
    {{- end }}
    {{- end }}

    # PostgreSQL配置
    postgresql.host={{ $root.Values.infrastructure.postgresql.host }}
    postgresql.port={{ $root.Values.infrastructure.postgresql.port }}
    postgresql.database=th_analysis
    postgresql.url=jdbc:postgresql://{{ $root.Values.infrastructure.postgresql.host }}:{{ $root.Values.infrastructure.postgresql.port }}/th_analysis?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT-8

    # 并行度配置
    {{- if $jobConfig.parallelism.kafkaSource }}
    parallelism.kafka.source={{ $jobConfig.parallelism.kafkaSource }}
    {{- end }}
    {{- if $jobConfig.parallelism.parsing }}
    parallelism.parsing={{ $jobConfig.parallelism.parsing }}
    {{- end }}
    {{- if $jobConfig.parallelism.processing }}
    parallelism.processing={{ $jobConfig.parallelism.processing }}
    {{- end }}
    # Redis配置
    redis.host={{ $root.Values.infrastructure.redis.host }}
    redis.port={{ $root.Values.infrastructure.redis.port }}
    redis.timeout=10000
    redis.key.ttl=604800
    # Redis凭据通过环境变量传入

    # Nebula配置
    nebula.graph.addr={{ $root.Values.infrastructure.nebula.graphd.host }}:{{ $root.Values.infrastructure.nebula.graphd.port }}
    nebula.meta.addr={{ $root.Values.infrastructure.nebula.metad.host }}:{{ $root.Values.infrastructure.nebula.metad.port }}
    nebula.space.name={{ $root.Values.infrastructure.nebula.space.name }}
    nebula.pool.max.conn.size={{ $root.Values.infrastructure.nebula.pool.maxConnSize }}
    nebula.pool.min.conn.size={{ $root.Values.infrastructure.nebula.pool.minConnSize }}
    nebula.pool.idle.time={{ $root.Values.infrastructure.nebula.pool.idleTime }}
    nebula.pool.timeout={{ $root.Values.infrastructure.nebula.pool.timeout }}
    nebula.session.size={{ $root.Values.infrastructure.nebula.sessionSize }}

    # Nebula批处理配置
    nebula.batch.size={{ $root.Values.infrastructure.nebula.batch.size | default "100" }}
    nebula.batch.interval={{ $root.Values.infrastructure.nebula.batch.interval | default "1000" }}
    {{- if $jobConfig.parallelism.elasticsearchSink }}
    parallelism.elasticsearch.sink={{ $jobConfig.parallelism.elasticsearchSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.kafkaSink }}
    parallelism.kafka.sink={{ $jobConfig.parallelism.kafkaSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.kafkaJsonSink }}
    parallelism.kafka.json-sink={{ $jobConfig.parallelism.kafkaJsonSink }}
    {{- end }}
    {{- if $jobConfig.parallelism.connectInfoUpdate }}
    parallelism.connect-info.update={{ $jobConfig.parallelism.connectInfoUpdate }}
    {{- end }}

    {{- if eq $jobName "flink-session-threat-detector" }}
    # Threat Detector 专用配置
    {{- if $jobConfig.threatDetector }}
    threat.detector.enabled={{ $jobConfig.threatDetector.enabled | default "true" }}
    {{- if $jobConfig.threatDetector.parallelism }}
    threat.detector.parallelism={{ $jobConfig.threatDetector.parallelism | default "4" }}
    {{- end }}
    {{- if $jobConfig.threatDetector.bufferSize }}
    threat.detector.buffer.size={{ $jobConfig.threatDetector.bufferSize | default "1000" }}
    {{- end }}
    {{- if $jobConfig.threatDetector.timeoutMs }}
    threat.detector.timeout.ms={{ $jobConfig.threatDetector.timeoutMs | default "30000" }}
    {{- end }}
    {{- if $jobConfig.threatDetector.debugEnabled }}
    threat.detector.debug.enabled={{ $jobConfig.threatDetector.debugEnabled | default "false" }}
    {{- end }}
    {{- end }}

    # 威胁检测器状态管理配置
    {{- if $jobConfig.stateManagement }}
    threat.detector.state.ttl.default={{ $jobConfig.stateManagement.ttl.default | default "86400" }}
    threat.detector.state.ttl.attack-chain={{ $jobConfig.stateManagement.ttl.attackChain | default "604800" }}
    threat.detector.state.ttl.detector={{ $jobConfig.stateManagement.ttl.detector | default "2592000" }}
    {{- else }}
    threat.detector.state.ttl.default=86400
    threat.detector.state.ttl.attack-chain=604800
    threat.detector.state.ttl.detector=2592000
    {{- end }}

    # 攻击链分析配置
    {{- if $jobConfig.attackChain }}
    attack.chain.analysis.enabled={{ $jobConfig.attackChain.analysisEnabled | default "true" }}
    attack.chain.correlation.window.minutes={{ $jobConfig.attackChain.correlationWindowMinutes | default "30" }}
    attack.chain.attach.result={{ $jobConfig.attackChain.attachResult | default "true" }}
    attack.chain.cleanup.interval.hours={{ $jobConfig.attackChain.cleanupIntervalHours | default "6" }}
    {{- else }}
    attack.chain.analysis.enabled=true
    attack.chain.correlation.window.minutes=30
    attack.chain.attach.result=true
    attack.chain.cleanup.interval.hours=6
    {{- end }}

    # Flink状态配置
    {{- if $jobConfig.flinkState }}
    flink.checkpoint.interval={{ $jobConfig.flinkState.checkpointInterval | default "60000" }}
    flink.checkpoint.mode={{ $jobConfig.flinkState.checkpointMode | default "EXACTLY_ONCE" }}
    flink.state.backend={{ $jobConfig.flinkState.backend | default "rocksdb" }}
    flink.state.ttl.detection-context={{ $jobConfig.flinkState.ttl.detectionContext | default "3600" }}
    flink.state.ttl.recent-events={{ $jobConfig.flinkState.ttl.recentEvents | default "1800" }}
    flink.state.ttl.window-data={{ $jobConfig.flinkState.ttl.windowData | default "7200" }}
    {{- else }}
    flink.checkpoint.interval=60000
    flink.checkpoint.mode=EXACTLY_ONCE
    flink.state.backend=rocksdb
    flink.state.ttl.detection-context=3600
    flink.state.ttl.recent-events=1800
    flink.state.ttl.window-data=7200
    {{- end }}

    # Threat Detector 输出配置
    {{- if $jobConfig.output }}
    {{- if $jobConfig.output.sessionLabelParallelism }}
    threat.output.session.label.parallelism={{ $jobConfig.output.sessionLabelParallelism | default "2" }}
    {{- end }}
    {{- if $jobConfig.output.assetLabelParallelism }}
    threat.output.asset.label.parallelism={{ $jobConfig.output.assetLabelParallelism | default "2" }}
    {{- end }}
    {{- if $jobConfig.output.alarmParallelism }}
    threat.output.alarm.parallelism={{ $jobConfig.output.alarmParallelism | default "4" }}
    {{- end }}
    {{- if $jobConfig.output.notificationParallelism }}
    threat.output.notification.parallelism={{ $jobConfig.output.notificationParallelism | default "2" }}
    {{- end }}
    {{- end }}

    # Threat Detector 专用主题配置
    {{- if $jobConfig.topics }}
    {{- if $jobConfig.topics.sessionMetadata }}
    kafka.topic.session.metadata={{ $jobConfig.topics.sessionMetadata }}
    {{- end }}
    {{- if $jobConfig.topics.protocolMetadata }}
    kafka.topic.protocol.metadata={{ $jobConfig.topics.protocolMetadata }}
    {{- end }}
    {{- if $jobConfig.topics.threatConfig }}
    kafka.topic.threat.config={{ $jobConfig.topics.threatConfig }}
    {{- end }}
    {{- if $jobConfig.topics.threatAlarms }}
    kafka.topic.threat.alarms={{ $jobConfig.topics.threatAlarms }}
    {{- end }}
    {{- if $jobConfig.topics.threatNotifications }}
    kafka.topic.threat.notifications={{ $jobConfig.topics.threatNotifications }}
    {{- end }}
    {{- end }}
    {{- end }}

    {{- if eq $jobName "flink-certificate-analyzer" }}
    # Certificate Analyzer 专用配置
    {{- if $jobConfig.certificateAnalyzer }}
    certificate.analyzer.enabled={{ $jobConfig.certificateAnalyzer.enabled | default "true" }}
    {{- if $jobConfig.certificateAnalyzer.parallelism }}
    certificate.analyzer.parallelism={{ $jobConfig.certificateAnalyzer.parallelism | default "4" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.bufferSize }}
    certificate.analyzer.buffer.size={{ $jobConfig.certificateAnalyzer.bufferSize | default "1000" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.timeoutMs }}
    certificate.analyzer.timeout.ms={{ $jobConfig.certificateAnalyzer.timeoutMs | default "30000" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.debugEnabled }}
    certificate.analyzer.debug.enabled={{ $jobConfig.certificateAnalyzer.debugEnabled | default "false" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.lmdbEnabled }}
    certificate.analyzer.lmdb.enabled={{ $jobConfig.certificateAnalyzer.lmdbEnabled | default "false" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.postgresqlEnabled }}
    certificate.analyzer.postgresql.enabled={{ $jobConfig.certificateAnalyzer.postgresqlEnabled | default "false" }}
    {{- end }}
    {{- if $jobConfig.certificateAnalyzer.nebulaEnabled }}
    certificate.analyzer.nebula.enabled={{ $jobConfig.certificateAnalyzer.nebulaEnabled | default "true" }}
    {{- end }}
    {{- end }}

    # Certificate Analyzer 输出配置
    {{- if $jobConfig.output }}
    {{- if $jobConfig.output.esUserIndex }}
    certificate.analyzer.es.user.index={{ $jobConfig.output.esUserIndex | default "cert_user" }}
    {{- end }}
    {{- if $jobConfig.output.esSystemIndex }}
    certificate.analyzer.es.system.index={{ $jobConfig.output.esSystemIndex | default "cert_system" }}
    {{- end }}
    {{- end }}

    # Certificate Analyzer 专用主题配置
    {{- if $jobConfig.topics }}
    {{- if $jobConfig.topics.certificateFiles }}
    kafka.topic.certificate.files={{ $jobConfig.topics.certificateFiles }}
    {{- end }}
    {{- if $jobConfig.topics.systemCertificates }}
    kafka.topic.system.certificates={{ $jobConfig.topics.systemCertificates }}
    {{- end }}
    {{- end }}
    {{- end }}

    {{- if eq $jobName "flink-alarm-processor" }}
    # ==================== 告警处理器配置 ====================
    # 作业基础配置
    alarm.processor.job.name={{ $jobConfig.job.name | default "alarm-processor" }}
    alarm.processor.job.parallelism={{ $jobConfig.job.parallelism | default "4" }}
    alarm.processor.job.checkpointInterval={{ $jobConfig.job.checkpointInterval | default "60000" }}
    alarm.processor.job.restartAttempts={{ $jobConfig.job.restartAttempts | default "3" }}
    alarm.processor.job.restartDelay={{ $jobConfig.job.restartDelay | default "10000" }}

    # Alarm Processor 专用配置
    {{- if $jobConfig.alarmProcessor }}
    alarm.processor.enabled={{ $jobConfig.alarmProcessor.enabled | default "true" }}
    {{- if $jobConfig.alarmProcessor.parallelism }}
    alarm.processor.parallelism={{ $jobConfig.alarmProcessor.parallelism | default "4" }}
    {{- end }}
    {{- if $jobConfig.alarmProcessor.bufferSize }}
    alarm.processor.buffer.size={{ $jobConfig.alarmProcessor.bufferSize | default "1000" }}
    {{- end }}
    {{- if $jobConfig.alarmProcessor.timeoutMs }}
    alarm.processor.timeout.ms={{ $jobConfig.alarmProcessor.timeoutMs | default "30000" }}
    {{- end }}
    {{- if $jobConfig.alarmProcessor.debugEnabled }}
    alarm.processor.debug.enabled={{ $jobConfig.alarmProcessor.debugEnabled | default "false" }}
    {{- end }}
    {{- end }}

    # Alarm Processor Kafka 配置
    alarm.processor.kafka.bootstrapServers={{ $root.Values.infrastructure.kafka.host }}:{{ $root.Values.infrastructure.kafka.port }}
    {{- if $jobConfig.kafka }}
    {{- if $jobConfig.kafka.groupId }}
    alarm.processor.kafka.groupId={{ $jobConfig.kafka.groupId | default "alarm-processor-group" }}
    {{- else }}
    alarm.processor.kafka.groupId=alarm-processor-group
    {{- end }}
    {{- if $jobConfig.kafka.inputTopic }}
    alarm.processor.kafka.input.topic={{ $jobConfig.kafka.inputTopic | default "raw-alarms" }}
    {{- else }}
    alarm.processor.kafka.input.topic=raw-alarms
    {{- end }}
    {{- if $jobConfig.kafka.outputTopic }}
    alarm.processor.kafka.output.topic={{ $jobConfig.kafka.outputTopic | default "processed-alarms" }}
    {{- else }}
    alarm.processor.kafka.output.topic=processed-alarms
    {{- end }}
    {{- if $jobConfig.kafka.notificationTopic }}
    alarm.processor.kafka.notification.topic={{ $jobConfig.kafka.notificationTopic | default "alarm-notifications" }}
    {{- else }}
    alarm.processor.kafka.notification.topic=alarm-notifications
    {{- end }}
    alarm.processor.kafka.input.startingOffsets={{ $jobConfig.kafka.input.startingOffsets | default "latest" }}
    alarm.processor.kafka.input.autoCommit={{ $jobConfig.kafka.input.autoCommit | default "true" }}
    alarm.processor.kafka.input.commitInterval={{ $jobConfig.kafka.input.commitInterval | default "5000" }}
    {{- else }}
    alarm.processor.kafka.groupId=alarm-processor-group
    alarm.processor.kafka.input.topic=raw-alarms
    alarm.processor.kafka.output.topic=processed-alarms
    alarm.processor.kafka.notification.topic=alarm-notifications
    alarm.processor.kafka.input.startingOffsets=latest
    alarm.processor.kafka.input.autoCommit=true
    alarm.processor.kafka.input.commitInterval=5000
    {{- end }}

    # Alarm Processor PostgreSQL 配置
    alarm.processor.postgresql.host={{ $root.Values.infrastructure.postgresql.host }}
    alarm.processor.postgresql.port={{ $root.Values.infrastructure.postgresql.port }}
    {{- if $jobConfig.postgresql }}
    alarm.processor.postgresql.database={{ $jobConfig.postgresql.database | default "nta" }}
    alarm.processor.postgresql.table={{ $jobConfig.postgresql.table | default "alarm_records" }}
    alarm.processor.postgresql.username={{ $jobConfig.postgresql.username | default "nta_user" }}
    alarm.processor.postgresql.password={{ $jobConfig.postgresql.password | default "nta_password" }}
    {{- else }}
    alarm.processor.postgresql.database=nta
    alarm.processor.postgresql.username=nta_user
    alarm.processor.postgresql.password=nta_password
    {{- end }}

    # Alarm Processor 处理配置
    {{- if $jobConfig.processing }}
    {{- if $jobConfig.processing.deduplication }}
    alarm.processor.processing.deduplication.enabled={{ $jobConfig.processing.deduplication.enabled | default "true" }}
    alarm.processor.processing.deduplication.mode={{ $jobConfig.processing.deduplication.mode | default "HASH_BASED" }}
    alarm.processor.processing.deduplication.timeWindowMs={{ $jobConfig.processing.deduplication.timeWindowMs | default "300000" }}
    alarm.processor.processing.deduplication.maxCacheSize={{ $jobConfig.processing.deduplication.maxCacheSize | default "10000" }}
    alarm.processor.processing.deduplication.cacheExpirationMs={{ $jobConfig.processing.deduplication.cacheExpirationMs | default "600000" }}
    {{- else }}
    alarm.processor.processing.deduplication.enabled=true
    alarm.processor.processing.deduplication.mode=HASH_BASED
    alarm.processor.processing.deduplication.timeWindowMs=300000
    alarm.processor.processing.deduplication.maxCacheSize=10000
    alarm.processor.processing.deduplication.cacheExpirationMs=600000
    {{- end }}
    {{- if $jobConfig.processing.formatting }}
    alarm.processor.processing.formatting.enabled={{ $jobConfig.processing.formatting.enabled | default "true" }}
    alarm.processor.processing.formatting.includeReasonAnalysis={{ $jobConfig.processing.formatting.includeReasonAnalysis | default "true" }}
    alarm.processor.processing.formatting.includeHandlingSuggestions={{ $jobConfig.processing.formatting.includeHandlingSuggestions | default "true" }}
    {{- else }}
    alarm.processor.processing.formatting.enabled=true
    alarm.processor.processing.formatting.includeReasonAnalysis=true
    alarm.processor.processing.formatting.includeHandlingSuggestions=true
    {{- end }}
    {{- if $jobConfig.processing.attackChain }}
    alarm.processor.processing.attackChain.enabled={{ $jobConfig.processing.attackChain.enabled | default "true" }}
    alarm.processor.processing.attackChain.correlationWindowMs={{ $jobConfig.processing.attackChain.correlationWindowMs | default "1800000" }}
    alarm.processor.processing.attackChain.maxCacheSize={{ $jobConfig.processing.attackChain.maxCacheSize | default "5000" }}
    alarm.processor.processing.attackChain.minEventsForChain={{ $jobConfig.processing.attackChain.minEventsForChain | default "2" }}
    {{- else }}
    alarm.processor.processing.attackChain.enabled=true
    alarm.processor.processing.attackChain.correlationWindowMs=1800000
    alarm.processor.processing.attackChain.maxCacheSize=5000
    alarm.processor.processing.attackChain.minEventsForChain=2
    {{- end }}
    {{- if $jobConfig.processing.batch }}
    alarm.processor.processing.batch.enabled={{ $jobConfig.processing.batch.enabled | default "true" }}
    alarm.processor.processing.batch.maxBatchSize={{ $jobConfig.processing.batch.maxBatchSize | default "50" }}
    alarm.processor.processing.batch.maxWaitTimeMs={{ $jobConfig.processing.batch.maxWaitTimeMs | default "30000" }}
    alarm.processor.processing.batch.checkIntervalMs={{ $jobConfig.processing.batch.checkIntervalMs | default "5000" }}
    {{- else }}
    alarm.processor.processing.batch.enabled=true
    alarm.processor.processing.batch.maxBatchSize=50
    alarm.processor.processing.batch.maxWaitTimeMs=30000
    alarm.processor.processing.batch.checkIntervalMs=5000
    {{- end }}
    {{- else }}
    # 默认处理配置
    alarm.processor.processing.deduplication.enabled=true
    alarm.processor.processing.deduplication.mode=HASH_BASED
    alarm.processor.processing.deduplication.timeWindowMs=300000
    alarm.processor.processing.deduplication.maxCacheSize=10000
    alarm.processor.processing.deduplication.cacheExpirationMs=600000
    alarm.processor.processing.formatting.enabled=true
    alarm.processor.processing.formatting.includeReasonAnalysis=true
    alarm.processor.processing.formatting.includeHandlingSuggestions=true
    alarm.processor.processing.attackChain.enabled=true
    alarm.processor.processing.attackChain.correlationWindowMs=1800000
    alarm.processor.processing.attackChain.maxCacheSize=5000
    alarm.processor.processing.attackChain.minEventsForChain=2
    alarm.processor.processing.batch.enabled=true
    alarm.processor.processing.batch.maxBatchSize=50
    alarm.processor.processing.batch.maxWaitTimeMs=30000
    alarm.processor.processing.batch.checkIntervalMs=5000
    {{- end }}

    # Alarm Processor 输出配置
    {{- if $jobConfig.output }}
    {{- if $jobConfig.output.notification }}
    alarm.processor.output.notification.enabled={{ $jobConfig.output.notification.enabled | default "true" }}
    alarm.processor.output.notification.batchSize={{ $jobConfig.output.notification.batchSize | default "50" }}
    alarm.processor.output.notification.lingerMs={{ $jobConfig.output.notification.lingerMs | default "1000" }}
    {{- end }}
    {{- end }}

    # Alarm Processor 监控配置
    {{- if $jobConfig.monitoring }}
    alarm.processor.monitoring.enabled={{ $jobConfig.monitoring.enabled | default "true" }}
    alarm.processor.monitoring.metricsInterval={{ $jobConfig.monitoring.metricsInterval | default "30000" }}
    alarm.processor.monitoring.performanceLogging={{ $jobConfig.monitoring.performanceLogging | default "true" }}
    alarm.processor.monitoring.detailedMetrics={{ $jobConfig.monitoring.detailedMetrics | default "false" }}
    {{- else }}
    alarm.processor.monitoring.enabled=true
    alarm.processor.monitoring.metricsInterval=30000
    alarm.processor.monitoring.performanceLogging=true
    alarm.processor.monitoring.detailedMetrics=false
    {{- end }}
    
    # 通知统计配置
    alarm.processor.notification.statisticsIntervalSeconds={{ $jobConfig.notification.statisticsIntervalSeconds | default "300" }}
    
    # 告警抑制规则配置
    alarm.processor.suppression.alarmServiceBaseUrl={{ $jobConfig.suppression.alarmServiceBaseUrl | default "http://alarm-service:8080" }}
    alarm.processor.suppression.timeoutMs={{ $jobConfig.suppression.timeoutMs | default "5000" }}
    alarm.processor.suppression.retryCount={{ $jobConfig.suppression.retryCount | default "3" }}

    # Alarm Processor 并行度配置
    {{- if $jobConfig.parallelism }}
    {{- if $jobConfig.parallelism.kafkaSource }}
    alarm.processor.parallelism.kafka.source={{ $jobConfig.parallelism.kafkaSource | default "4" }}
    {{- else }}
    alarm.processor.parallelism.kafka.source=4
    {{- end }}
    {{- if $jobConfig.parallelism.processing }}
    alarm.processor.parallelism.processing={{ $jobConfig.parallelism.processing | default "8" }}
    {{- else }}
    alarm.processor.parallelism.processing=8
    {{- end }}
    {{- if $jobConfig.parallelism.postgresqlSink }}
    alarm.processor.parallelism.postgresql.sink={{ $jobConfig.parallelism.postgresqlSink | default "2" }}
    {{- else }}
    alarm.processor.parallelism.postgresql.sink=2
    {{- end }}
    {{- if $jobConfig.parallelism.notificationSink }}
    alarm.processor.parallelism.notification.sink={{ $jobConfig.parallelism.notificationSink | default "2" }}
    {{- else }}
    alarm.processor.parallelism.notification.sink=2
    {{- end }}
    alarm.processor.parallelism.notification.processing={{ $jobConfig.parallelism.notificationProcessing | default "2" }}
    {{- else }}
    # 默认并行度配置
    alarm.processor.parallelism.kafka.source=4
    alarm.processor.parallelism.processing=8
    alarm.processor.parallelism.postgresql.sink=2
    alarm.processor.parallelism.notification.sink=2
    alarm.processor.parallelism.notification.processing=2
    {{- end }}
    {{- end }}

    {{- if eq $jobName "flink-traffic-etl-processor" }}
    # Data Warehouse Processor 专用配置
    {{- if $jobConfig.dataWarehouseProcessor }}
    data.warehouse.processor.enabled={{ $jobConfig.dataWarehouseProcessor.enabled | default "true" }}
    {{- if $jobConfig.dataWarehouseProcessor.parallelism }}
    data.warehouse.processor.parallelism={{ $jobConfig.dataWarehouseProcessor.parallelism | default "4" }}
    {{- end }}
    {{- end }}

    # 证书提取配置
    {{- if $jobConfig.certificateExtraction }}
    certificate.extraction.enabled={{ $jobConfig.certificateExtraction.enabled | default "false" }}
    {{- if $jobConfig.certificateExtraction.parallelism }}
    certificate.extraction.parallelism={{ $jobConfig.certificateExtraction.parallelism | default "2" }}
    {{- end }}
    {{- else }}
    certificate.extraction.enabled=false
    certificate.extraction.parallelism=2
    {{- end }}

    # Data Warehouse Processor 专用主题配置
    {{- if $jobConfig.topics }}
    {{- if $jobConfig.topics.certificateFiles }}
    kafka.topic.certificate.files={{ $jobConfig.topics.certificateFiles | default "certfile" }}
    {{- end }}
    {{- if $jobConfig.topics.systemCertificates }}
    kafka.topic.system.certificates={{ $jobConfig.topics.systemCertificates | default "certfile_system" }}
    {{- end }}
    {{- else }}
    kafka.topic.certificate.files=certfile
    kafka.topic.system.certificates=certfile_system
    {{- end }}
    {{- end }}

    {{- if eq $jobName "flink-alarm-notification" }}
    # ==================== 告警通知配置 ====================
    # 作业基础配置
    alarm.notification.job.name={{ $jobConfig.job.name | default "alarm-notification-job" }}
    alarm.notification.job.parallelism={{ $jobConfig.job.parallelism | default "4" }}
    alarm.notification.checkpoint.interval={{ $jobConfig.job.checkpointInterval | default "60000" }}
    alarm.notification.checkpoint.timeout={{ $jobConfig.job.checkpointTimeout | default "300000" }}
    alarm.notification.checkpoint.minPauseBetween={{ $jobConfig.job.checkpointMinPauseBetween | default "5000" }}
    alarm.notification.checkpoint.maxConcurrent={{ $jobConfig.job.checkpointMaxConcurrent | default "1" }}

    # Alarm Notification Kafka 配置
    alarm.notification.kafka.bootstrapServers={{ $root.Values.infrastructure.kafka.host }}:{{ $root.Values.infrastructure.kafka.port }}
    {{- if $jobConfig.kafka }}
    {{- if $jobConfig.kafka.groupId }}
    alarm.notification.consumer.group.id={{ $jobConfig.kafka.groupId | default "alarm-notification-consumer" }}
    {{- else }}
    alarm.notification.consumer.group.id=alarm-notification-consumer
    {{- end }}
    {{- if $jobConfig.kafka.inputTopic }}
    alarm.notification.input.topic={{ $jobConfig.kafka.inputTopic | default "alarm-notifications" }}
    {{- else }}
    alarm.notification.input.topic=alarm-notifications
    {{- end }}
    {{- if $jobConfig.kafka.subscriptionTopic }}
    alarm.notification.subscription.topic={{ $jobConfig.kafka.subscriptionTopic | default "notification-subscriptions" }}
    {{- else }}
    alarm.notification.subscription.topic=notification-subscriptions
    {{- end }}
    alarm.notification.starting.offsets={{ $jobConfig.kafka.startingOffsets | default "latest" }}
    alarm.notification.auto.commit={{ $jobConfig.kafka.autoCommit | default "true" }}
    alarm.notification.commit.interval={{ $jobConfig.kafka.commitInterval | default "5000" }}
    {{- else }}
    alarm.notification.consumer.group.id=alarm-notification-consumer
    alarm.notification.input.topic=alarm-notifications
    alarm.notification.subscription.topic=notification-subscriptions
    alarm.notification.starting.offsets=latest
    alarm.notification.auto.commit=true
    alarm.notification.commit.interval=5000
    {{- end }}

    # 告警服务配置
    {{- if $jobConfig.alarmService }}
    alarm.notification.alarm.service.base.url={{ $jobConfig.alarmService.endpoint | default "http://alarm-service:8080" }}
    alarm.notification.alarm.service.timeout={{ $jobConfig.alarmService.timeout | default "5000" }}
    alarm.notification.alarm.service.retry.attempts={{ $jobConfig.alarmService.retryAttempts | default "3" }}
    {{- else }}
    alarm.notification.alarm.service.base.url=http://alarm-service:8080
    alarm.notification.alarm.service.timeout=5000
    alarm.notification.alarm.service.retry.attempts=3
    {{- end }}

    # 通知配置
    {{- if $jobConfig.notification }}
    alarm.notification.batch.size={{ $jobConfig.notification.batchSize | default "10" }}
    alarm.notification.batch.timeout={{ $jobConfig.notification.batchTimeout | default "5000" }}
    alarm.notification.retry.attempts={{ $jobConfig.notification.retryAttempts | default "3" }}
    alarm.notification.retry.delay={{ $jobConfig.notification.retryDelay | default "1000" }}
    {{- else }}
    alarm.notification.batch.size=10
    alarm.notification.batch.timeout=5000
    alarm.notification.retry.attempts=3
    alarm.notification.retry.delay=1000
    {{- end }}

    # 邮件配置
    {{- if $jobConfig.email }}
    alarm.notification.email.smtp.host={{ $jobConfig.email.host | default "smtp.example.com" }}
    alarm.notification.email.smtp.port={{ $jobConfig.email.port | default "587" }}
    alarm.notification.email.username={{ $jobConfig.email.username | default "<EMAIL>" }}
    alarm.notification.email.password={{ $jobConfig.email.password | default "password" }}
    alarm.notification.email.from.address={{ $jobConfig.email.from | default "<EMAIL>" }}
    alarm.notification.email.ssl.enabled={{ $jobConfig.email.ssl | default "false" }}
    alarm.notification.email.tls.enabled={{ $jobConfig.email.tls | default "true" }}
    {{- else }}
    alarm.notification.email.smtp.host=smtp.example.com
    alarm.notification.email.smtp.port=587
    alarm.notification.email.username=<EMAIL>
    alarm.notification.email.password=password
    alarm.notification.email.from.address=<EMAIL>
    alarm.notification.email.ssl.enabled=false
    alarm.notification.email.tls.enabled=true
    {{- end }}

    # Kafka通知配置
    {{- if $jobConfig.kafkaNotification }}
    alarm.notification.kafka.notification.topic={{ $jobConfig.kafkaNotification.topic | default "notification-results" }}
    alarm.notification.kafka.notification.batch.size={{ $jobConfig.kafkaNotification.batchSize | default "100" }}
    alarm.notification.kafka.notification.linger.ms={{ $jobConfig.kafkaNotification.lingerMs | default "1000" }}
    {{- else }}
    alarm.notification.kafka.notification.topic=notification-results
    alarm.notification.kafka.notification.batch.size=100
    alarm.notification.kafka.notification.linger.ms=1000
    {{- end }}

    # 监控配置
    {{- if $jobConfig.monitoring }}
    alarm.notification.monitoring.enabled={{ $jobConfig.monitoring.enabled | default "true" }}
    alarm.notification.monitoring.metrics.interval={{ $jobConfig.monitoring.metricsInterval | default "30000" }}
    alarm.notification.monitoring.performance.logging={{ $jobConfig.monitoring.performanceLogging | default "true" }}
    alarm.notification.monitoring.detailed.metrics={{ $jobConfig.monitoring.detailedMetrics | default "false" }}
    {{- else }}
    alarm.notification.monitoring.enabled=true
    alarm.notification.monitoring.metrics.interval=30000
    alarm.notification.monitoring.performance.logging=true
    alarm.notification.monitoring.detailed.metrics=false
    {{- end }}

    # 并行度配置
    {{- if $jobConfig.parallelism }}
    {{- if $jobConfig.parallelism.kafkaSource }}
    alarm.notification.parallelism.kafka.source={{ $jobConfig.parallelism.kafkaSource | default "4" }}
    {{- else }}
    alarm.notification.parallelism.kafka.source=4
    {{- end }}
    {{- if $jobConfig.parallelism.subscriptionSource }}
    alarm.notification.parallelism.subscription.source={{ $jobConfig.parallelism.subscriptionSource | default "2" }}
    {{- else }}
    alarm.notification.parallelism.subscription.source=2
    {{- end }}
    {{- if $jobConfig.parallelism.processing }}
    alarm.notification.parallelism.processing={{ $jobConfig.parallelism.processing | default "8" }}
    {{- else }}
    alarm.notification.parallelism.processing=8
    {{- end }}
    {{- else }}
    # 默认并行度配置
    alarm.notification.parallelism.kafka.source=4
    alarm.notification.parallelism.subscription.source=2
    alarm.notification.parallelism.processing=8
    {{- end }}
    {{- end }}

{{- end -}}

{{- if .Values.infrastructure.flink.enabled }}
{{- $namespace := .Values.global.namespace }}

{{- if .Values.infrastructure.flink.jobs.session-threat-detector.enabled }}
{{- $configData := dict "jobName" "flink-session-threat-detector" "jobConfig" .Values.infrastructure.flink.jobs.session-threat-detector.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.certificate-analyzer.enabled }}
{{- $configData := dict "jobName" "flink-certificate-analyzer" "jobConfig" .Values.infrastructure.flink.jobs.certificate-analyzer.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.traffic-etl-processor.enabled }}
{{- $configData := dict "jobName" "flink-traffic-etl-processor" "jobConfig" .Values.infrastructure.flink.jobs.traffic-etl-processor.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.alarm-processor.enabled }}
{{- $configData := dict "jobName" "flink-alarm-processor" "jobConfig" .Values.infrastructure.flink.jobs.alarm-processor.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- if .Values.infrastructure.flink.jobs.alarm-notification.enabled }}
{{- $configData := dict "jobName" "flink-alarm-notification" "jobConfig" .Values.infrastructure.flink.jobs.alarm-notification.config "namespace" $namespace "root" . -}}
{{- include "nta.flinkConfigMap" $configData }}
---
{{- end }}

{{- end }}
